import torch
import torch.nn as nn
import torch.nn.functional as F


# 1. 2D残差块 (ResidualBlock2D):
class ResidualBlock2D(nn.Module):
    def __init__(self, in_c, out_c, stride=1, downsample=None, groups=8):
        super().__init__()
        self.conv1 = nn.Conv2d(in_c, out_c, 3, stride, 1, bias=False)
        self.gn1 = nn.GroupNorm(groups, out_c)  # 组归一化
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(out_c, out_c, 3, 1, 1, bias=False)
        self.gn2 = nn.GroupNorm(groups, out_c)
        self.downsample = downsample

    def forward(self, x):
        identity = x
        out = self.relu(self.gn1(self.conv1(x)))
        out = self.gn2(self.conv2(out))
        if self.downsample is not None:
            identity = self.downsample(x)
        out += identity
        return self.relu(out)


# 2. 通道注意力模块 (Channel Attention Module)
class ChannelAttention(nn.Module):
    def __init__(self, in_channels, reduction_ratio=8):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction_ratio),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction_ratio, in_channels),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        avg_out = self.fc(self.avg_pool(x).view(b, c))
        max_out = self.fc(self.max_pool(x).view(b, c))
        out = avg_out + max_out
        return torch.sigmoid(out).view(b, c, 1, 1) * x


# 3. 更小的ResNet主体 (ResNetMini2D):
class ResNetMini2D(nn.Module):
    def __init__(self, in_channels=2, groups=8, attention_factor=0.5):
        """
        参数:
            in_channels: 输入通道数
            groups: 组归一化的组数
            attention_factor: 注意力机制的权重因子
        """
        super().__init__()
        self.in_c = 32  # 减少初始通道数
        self.attention_factor = attention_factor

        # 初始卷积层 - 使用更小的卷积核
        self.conv1 = nn.Conv2d(in_channels, 32, 3, 1, 1, bias=False)  # 3x3卷积，步长1
        self.gn1 = nn.GroupNorm(groups, 32)
        self.relu = nn.ReLU(inplace=True)

        # 使用更少的残差层和更小的通道数
        # 构建3个残差层 (原为4个)
        self.layer1 = self._make_residual_block(32, 32, stride=1, groups=groups)
        self.layer2 = self._make_residual_block(32, 64, stride=2, groups=groups)
        self.layer3 = self._make_residual_block(64, 128, stride=2, groups=groups)

        # 注意力模块 - 减少到3个
        self.channel_att1 = ChannelAttention(32)
        self.channel_att2 = ChannelAttention(64)
        self.channel_att3 = ChannelAttention(128)

        # 输出层
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.head = nn.Sequential(
            nn.Linear(128, 64),  # 减少中间层大小
            nn.ReLU(True),
            nn.Dropout(0.3),
            nn.Linear(64, 1)  # 输出单一刚度值
        )

    def _make_residual_block(self, in_c, out_c, stride=1, groups=8):
        down = None
        if stride != 1 or in_c != out_c:
            down = nn.Sequential(
                nn.Conv2d(in_c, out_c, 1, stride, bias=False),
                nn.GroupNorm(groups, out_c)
            )
        return nn.Sequential(
            ResidualBlock2D(in_c, out_c, stride, down, groups),
            ResidualBlock2D(out_c, out_c, groups=groups)
        )

    def forward(self, x):
        # 初始卷积层
        x = self.relu(self.gn1(self.conv1(x)))

        # 残差层 + 通道注意力
        x = self.layer1(x)
        x = self.channel_att1(x) * self.attention_factor + x * (1 - self.attention_factor)

        x = self.layer2(x)
        x = self.channel_att2(x) * self.attention_factor + x * (1 - self.attention_factor)

        x = self.layer3(x)
        x = self.channel_att3(x) * self.attention_factor + x * (1 - self.attention_factor)

        # 全局平均池化和全连接层
        x = self.avgpool(x).flatten(1)
        out = self.head(x)
        return out


# 4. 创建ResNet-9模型
def ResNet_9_2D(in_channels=3, attention_factor=0.5):
    return ResNetMini2D(
        in_channels=in_channels,
        attention_factor=attention_factor
    )


# # 验证
# if __name__ == "__main__":
#     # 创建模型
#     model = ResNet_9_2D(in_channels=2, attention_factor=0.5)
#     print("ResNet-9 参数数量:", sum(p.numel() for p in model.parameters() if p.requires_grad))
#
#     # 假设输入是双通道灰度图 (batch_size=4, 通道=2, 高度=256, 宽度=256)
#     input_tensor = torch.randn(4, 2, 256, 256)
#
#     # 前向传播
#     output = model(input_tensor)
#     print("输出形状:", output.shape)  # 输出: torch.Size([4, 1])