import os
import numpy as np
import torch
from torch.utils.data import Dataset
from PIL import Image
import re
import torchvision.transforms as transforms
class CarDoorStiffnessDataset(Dataset):
    def __init__(self, root_dir, minite_dir="minite_points", thickness_dir="other_thickness",
                 combination_dir="thickness_combination",
                 data_file="extracted_data.npy", transform=None, augment=False):
        """
        车门刚度数据集加载器（三通道版）
        参数:
            root_dir: 根目录路径 (包含minite_points和other_thickness文件夹)
            minite_dir: minite_points文件夹名称
            thickness_dir: other_thickness文件夹名称
            data_file: 包含刚度值的npy文件名
            transform: 可选的数据增强/预处理变换
            augment: 是否应用数据增强
        """
        self.root_dir = root_dir   # 传入的参数 root_dir 赋值给类的实例变量 self.root_dir
        self.transform = transform
        self.augment = augment
        self.samples = []  # 存储样本信息: (minite_path, thickness_path, stiffness)
        self.stiffness_dict = None

        # 构建完整路径
        minite_path = os.path.join(root_dir, minite_dir)
        thickness_path = os.path.join(root_dir, thickness_dir)
        comb_path = os.path.join(root_dir, combination_dir)
        data_path = os.path.join(root_dir, data_file)

        # 验证路径存在
        if not os.path.isdir(minite_path):
            raise FileNotFoundError(f"minite_points目录不存在: {minite_path}")
        if not os.path.isdir(thickness_path):
            raise FileNotFoundError(f"other_thickness目录不存在: {thickness_path}")
        if not os.path.isfile(data_path):
            raise FileNotFoundError(f"刚度数据文件不存在: {data_path}")

        # 加载刚度数据字典
        stiffness_data = np.load(data_path, allow_pickle=True).item()
        print('原始刚度数据:', stiffness_data)

        # 创建从文件路径到刚度值的映射字典
        self.stiffness_dict = {}
        # 假设每个文件路径对应一个刚度值
        for path, stiffness_value in zip(stiffness_data['columns'], stiffness_data['data']):
            # 从路径中提取run_id
            match = re.search(r'run[-_](\d{5})', path)
            if match:
                run_id = match.group(1)
                stiffness_key = f"run_{run_id}"
                self.stiffness_dict[stiffness_key] = stiffness_value
        print('重构后的刚度字典:', self.stiffness_dict)

        # 收集minite_points文件夹中的所有图像文件
        minite_files = [f for f in os.listdir(minite_path)   # minite_points文件夹中路径下的文件列表。
                       if f.endswith('.png') and "components_multi" in f]
        # minite_files:['R-001_00001_components_multi.png', 'R-001_00002_components_multi.png']

        # 为每个minite文件匹配对应的thickness文件和刚度值
        for minite_file in minite_files:
            # 提取run编号 (例如从"R-001_00001_components_multi.png"中提取"00001")
            match = re.search(r'[-_](\d{5})[-_]components_multi\.png$', minite_file)
            if not match:
                continue

            run_id = match.group(1)  # 例如 "00001"
            stiffness_key = f"run_{run_id}"   # 例如 "run_00001" 因为result,csv中名字

            # 检查刚度数据中是否存在对应的run
            if stiffness_key not in self.stiffness_dict:
                print(f"警告: 刚度数据中没有找到 {stiffness_key}")
                continue

            # 构建thickness文件名 (与minite文件同名)
            thickness_file = minite_file
            comb_file = minite_file  # 厚度组合图像使用相同文件名
            minite_full = os.path.join(minite_path, minite_file)
            thickness_full = os.path.join(thickness_path, thickness_file)
            comb_full = os.path.join(comb_path, comb_file)
            # 检查thickness文件是否存在
            if not os.path.isfile(thickness_full):
                continue

            # 存储样本信息:将同一车门的厚度灰度图，中间板灰度图以及对应刚度数据放到一个字典中
            # 存储样本信息
            self.samples.append({
                'minite': minite_full,
                'thickness': thickness_full,
                'combination': comb_full,  # 新增厚度组合通道
                'stiffness': self.stiffness_dict[stiffness_key],
                'run_id': run_id
            })

        if not self.samples:
            raise RuntimeError(f"未找到有效的样本: {root_dir}")
        # 记录图像尺寸（用于后续数据增强）
        sample_img = Image.open(self.samples[0]['minite'])
        self.img_width, self.img_height = sample_img.size
        print(f"图像尺寸: {self.img_width}x{self.img_height}")
    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]

        # 加载三个通道的图像
        minite_img = Image.open(sample['minite'])         # 通道1: 中间板
        thickness_img = Image.open(sample['thickness'])   # 通道2: 厚度
        comb_img = Image.open(sample['combination'])      # 通道3: 厚度组合
        # 应用数据增强（在PIL图像阶段进行）
        if self.augment:
            # 生成随机增强参数（确保三个图像使用相同的增强参数）
            # flip_h = np.random.rand() > 0.5  # 水平翻转
            angle = np.random.uniform(-5, 5)  # 旋转角度

            # 定义应用增强的函数
            def apply_augment(img):
                # if flip_h:
                #     img = img.transpose(Image.FLIP_LEFT_RIGHT)
                if abs(angle) > 0.1:
                    img = img.rotate(angle, resample=Image.BILINEAR)
                return img
            # 对三个通道应用相同的增强
            minite_img = apply_augment(minite_img)
            thickness_img = apply_augment(thickness_img)
            comb_img = apply_augment(comb_img)
        # 应用基础变换（如果有）
        if self.transform:
            minite_img = self.transform(minite_img)
            thickness_img = self.transform(thickness_img)
            comb_img = self.transform(comb_img)

        # 创建通道图像,转换为数组，还有Transform.ToTensor的方式
        minite_array = np.array(minite_img)
        thickness_array = np.array(thickness_img)
        comb_array = np.array(comb_img)

        # 检查图像尺寸一致性
        shapes = [minite_array.shape, thickness_array.shape, comb_array.shape]
        if not all(sh == shapes[0] for sh in shapes):
            # 尝试调整尺寸（如果尺寸不匹配）
            target_size = minite_array.shape
            if thickness_array.shape != target_size:
                thickness_img = thickness_img.resize((target_size[1], target_size[0]), Image.BILINEAR)
                thickness_array = np.array(thickness_img)
            if comb_array.shape != target_size:
                comb_img = comb_img.resize((target_size[1], target_size[0]), Image.BILINEAR)
                comb_array = np.array(comb_img)

            # 再次检查
            if thickness_array.shape != target_size or comb_array.shape != target_size:
                raise RuntimeError(
                    f"图像尺寸不匹配: minite({minite_array.shape}), "
                    f"thickness({thickness_array.shape}), combination({comb_array.shape})"
                )

        # 合并为三通道图像 [H, W, 3]
        triple_channel = np.stack([minite_array, thickness_array, comb_array], axis=-1)

        # 转换为PyTorch张量并归一化
        triple_channel = triple_channel.astype(np.float32) / 255.0

        # 转换为CHW格式 [3, H, W] 神经网络中的输入通常要求数据的格式是 [C, H, W] H：表示图像的高度（像素的行数）
        triple_channel = np.transpose(triple_channel, (2, 0, 1))
        triple_channel = torch.from_numpy(triple_channel)

        # 获取刚度值
        stiffness = sample['stiffness']

        return (
            triple_channel,  # 三通道图像
            torch.tensor([stiffness], dtype=torch.float32),  # 刚度值
            sample['run_id']  # run_id用于调试
        )




# # 使用示例
"""
train.py 调用时只需如下操作
def train():
    # 1. 准备数据集
    # 基础变换 - 转换为张量
    base_transform = transforms.Compose([
        transforms.Grayscale(num_output_channels=1),  # 确保为单通道
    ])
    # 1. 准备数据集和数据加载器
    train_dataset = CarDoorStiffnessDataset(
        root_dir=TRAIN_PATH,
        minite_dir="minite_points",
        thickness_dir="other_thickness",
        combination_dir="thickness_combination",
        data_file="extracted_data.npy",
        transform=base_transform,
        augment=True  # 不在初始化时应用增强
    )
"""

"""
验证集，确保在验证集上不启用数据增强（保持augment=False）

"""