import torch
import torch.nn as nn
import torch.nn.functional as F
# 2D残差块 (用于ResNet18/34)
class ResidualBlock2D(nn.Module):
    def __init__(self, in_c, out_c, stride=1, downsample=None, groups=8):
        super().__init__()
        self.conv1 = nn.Conv2d(in_c, out_c, kernel_size=3, stride=stride, padding=1, bias=False)
        self.gn1   = nn.GroupNorm(groups, out_c)
        self.relu  = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(out_c, out_c, kernel_size=3, stride=1, padding=1, bias=False)
        self.gn2   = nn.GroupNorm(groups, out_c)
        self.downsample = downsample

    def forward(self, x):
        identity = x
        out = self.relu(self.gn1(self.conv1(x)))
        out = self.gn2(self.conv2(out))
        if self.downsample is not None:
            identity = self.downsample(x)
        out += identity
        return self.relu(out)
# 通道注意力模块
class ChannelAttention(nn.Module):
    def __init__(self, in_channels, reduction_ratio=8):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction_ratio),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction_ratio, in_channels),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        avg_out = self.fc(self.avg_pool(x).view(b, c))
        max_out = self.fc(self.max_pool(x).view(b, c))
        out = avg_out + max_out
        return torch.sigmoid(out).view(b, c, 1, 1) * x
# 3. ResNet主体 (ResNet2D):
class ResNet2D(nn.Module):
    def __init__(self, block, layers, in_channels=2, groups=8, attention_factor=0.5):
        """
        参数:
            in_channels: 输入通道数 (默认2个: 中间板厚度和中间板图像)
            groups: 组归一化的组数
            attention_factor: 注意力机制的权重因子
        """
        super().__init__()
        self.in_c = 64
        self.attention_factor = attention_factor  # 注意力
        # 初始卷积层
        self.conv1 = nn.Conv2d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.gn1   = nn.GroupNorm(groups, 64)
        self.relu  = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)

        # ResNet34层配置: [3, 4, 6, 3] 构建4个残差层
        self.layer1 = self._make_layer(block, 64,  layers[0], groups=groups)
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2, groups=groups)
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2, groups=groups)
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2, groups=groups)

        # 注意力模块
        self.channel_att1 = ChannelAttention(64)
        self.channel_att2 = ChannelAttention(128)
        self.channel_att3 = ChannelAttention(256)
        self.channel_att4 = ChannelAttention(512)

        # 输出层
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.head = nn.Sequential(
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 1)  # 输出一个回归值（如刚度）
        )

    def _make_layer(self, block, out_c, blocks, stride=1, groups=8):
        down = None
        if stride != 1 or self.in_c != out_c:
            down = nn.Sequential(
                nn.Conv2d(self.in_c, out_c, kernel_size=1, stride=stride, bias=False),
                nn.GroupNorm(groups, out_c)
            )
        layers = [block(self.in_c, out_c, stride, down, groups)]
        self.in_c = out_c
        layers += [block(out_c, out_c, groups=groups) for _ in range(1, blocks)]
        return nn.Sequential(*layers)

    # def forward(self, x):
    #     x = self.relu(self.gn1(self.conv1(x)))
    #     x = self.maxpool(x)
    #     x = self.layer1(x)
    #     x = self.layer2(x)
    #     x = self.layer3(x)
    #     x = self.layer4(x)
    #     x = self.avgpool(x).flatten(1)
    #     out = self.head(x)
    #     return out  # shape: (B, 1)
    def forward(self, x):
        # x shape: (B, C, H, W) - 多通道灰度图

        # 初始卷积层
        x = self.relu(self.gn1(self.conv1(x)))
        x = self.maxpool(x)

        # 残差层 + 通道注意力
        x = self.layer1(x)
        x = self.channel_att1(x) * self.attention_factor + x * (1 - self.attention_factor)

        x = self.layer2(x)
        x = self.channel_att2(x) * self.attention_factor + x * (1 - self.attention_factor)

        x = self.layer3(x)
        x = self.channel_att3(x) * self.attention_factor + x * (1 - self.attention_factor)

        x = self.layer4(x)
        x = self.channel_att4(x) * self.attention_factor + x * (1 - self.attention_factor)

        # 全局平均池化和全连接层
        x = self.avgpool(x).flatten(1)
        out = self.head(x)  # 输出形状 (B, 1)
        return out
def ResNet_34_2D(in_channels=3, attention_factor=0.5):
    return ResNet2D(ResidualBlock2D, [3, 4, 6, 3], in_channels=in_channels, attention_factor=attention_factor)


# # 验证
# if __name__ == "__main__":
#     # 创建模型
#     model = ResNet_34_2D(in_channels=2, attention_factor=0.5)
#
#     # 假设输入是双通道灰度图 (batch_size=4, 通道=2, 高度=256, 宽度=256)
#     input_tensor = torch.randn(4, 2, 256, 256)
#
#     # 前向传播
#     output = model(input_tensor)
#     print(output.shape)  # 输出: torch.Size([4, 1])