import os
import re
from pathlib import Path


def rename_files(folder_path: str) -> None:
    """
    批量重命名指定文件夹中的文件

    参数:
        folder_path: 包含待处理文件的文件夹路径
    """
    # 验证文件夹路径
    folder = Path(folder_path)
    if not folder.exists() or not folder.is_dir():
        raise ValueError(f"指定的路径不存在或不是一个文件夹: {folder_path}")

    # 从文件夹名称中提取前缀 (支持 F-XXX-XX 格式，X可以是字母或数字)
    folder_name = folder.name
    prefix_match = re.match(r'F-([a-zA-Z0-9]+)-([a-zA-Z0-9]+)', folder_name)
    if not prefix_match:
        raise ValueError(f"文件夹名称不符合F-XXX-XX格式: {folder_name}")

    prefix_part1 = prefix_match.group(1)  # 第一组字符 (X部分)
    new_prefix = f"F-{prefix_part1}"

    # 处理文件夹中的文件
    for file in folder.iterdir():
        if file.is_file():
            # 匹配文件格式 (支持 calculation__NNNNN_components.pkl 格式)
            file_match = re.match(r'calculation__(\d+)_components\.pkl', file.name)
            if file_match:
                number_part = file_match.group(1)  # 数字部分
                new_name = f"{new_prefix}-{number_part}-components.pkl"
                new_path = folder / new_name

                # 执行重命名
                try:
                    file.rename(new_path)
                    print(f"已重命名: {file.name} -> {new_name}")
                except Exception as e:
                    print(f"重命名失败 {file.name}: {str(e)}")


def main():
    folder_path = input("请输入F-XXX-XX格式的文件夹路径: ").strip()

    try:
        rename_files(folder_path)
        print("重命名操作完成！")
    except Exception as e:
        print(f"错误: {str(e)}")


if __name__ == "__main__":
    main()    