import torch
import torch.nn as nn

class BottleneckBlock3D(nn.Module):
    expansion = 4  # 扩展倍数：输出通道 = base_out_c * 4

    def __init__(self, in_c, out_c, stride=1, downsample=None, groups=8):
        super().__init__()
        mid_c = out_c // self.expansion
        self.conv1 = nn.Conv3d(in_c, mid_c, kernel_size=1, bias=False)
        self.gn1   = nn.GroupNorm(groups, mid_c)

        self.conv2 = nn.Conv3d(mid_c, mid_c, kernel_size=3, stride=stride, padding=1, bias=False)
        self.gn2   = nn.GroupNorm(groups, mid_c)

        self.conv3 = nn.Conv3d(mid_c, out_c, kernel_size=1, bias=False)
        self.gn3   = nn.GroupNorm(groups, out_c)

        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample

    def forward(self, x):
        identity = x

        out = self.relu(self.gn1(self.conv1(x)))
        out = self.relu(self.gn2(self.conv2(out)))
        out = self.gn3(self.conv3(out))

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        return self.relu(out)

class ResNet3D(nn.Module):
    def __init__(self, block, layers, in_channels=5, groups=8):
        super().__init__()
        self.in_c = 64
        self.conv1 = nn.Conv3d(in_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.gn1   = nn.GroupNorm(groups, 64)
        self.relu  = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool3d(kernel_size=3, stride=2, padding=1)

        self.layer1 = self._make_layer(block, 256,  layers[0], stride=1, groups=groups)  # out = 64 * 4
        self.layer2 = self._make_layer(block, 512,  layers[1], stride=2, groups=groups)  # out = 128 * 4
        self.layer3 = self._make_layer(block, 1024, layers[2], stride=2, groups=groups)  # out = 256 * 4
        self.layer4 = self._make_layer(block, 2048, layers[3], stride=2, groups=groups)  # out = 512 * 4

        self.avgpool = nn.AdaptiveAvgPool3d((1, 1, 1))
        self.head = nn.Sequential(
            nn.Linear(2048, 512),
            nn.ReLU(inplace=True),
            nn.Linear(512, 1)  # 输出单一回归值
        )

    def _make_layer(self, block, out_c, blocks, stride=1, groups=8):
        down = None
        if stride != 1 or self.in_c != out_c:
            down = nn.Sequential(
                nn.Conv3d(self.in_c, out_c, kernel_size=1, stride=stride, bias=False),
                nn.GroupNorm(groups, out_c)
            )
        layers = [block(self.in_c, out_c, stride, down, groups)]
        self.in_c = out_c
        for _ in range(1, blocks):
            layers.append(block(self.in_c, out_c, groups=groups))
        return nn.Sequential(*layers)

    def forward(self, x):
        x = self.relu(self.gn1(self.conv1(x)))
        x = self.maxpool(x)
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        x = self.avgpool(x).flatten(1)
        return self.head(x)

def ResNet_50(in_channels=5):
    return ResNet3D(BottleneckBlock3D, [3, 4, 6, 3], in_channels=in_channels)
