import time
import os
import torch
import numpy as np
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau, StepLR
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import torch.nn as nn
import torchvision.transforms as transforms

# ----------------本训练代码输入数据是三中间板通道，更改后实现数据集的划分
# 设置中文字体支持（如果不需要中文标签可删除）
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
# 导入新的数据集类和模型
from dataset_tongdao import CarDoorStiffnessDataset  # 替换为三通道灰度图数据集类
from model import ResNet_18_2D  # 替换为您的2D ResNet模型

# from model34_new import ResNet_34_2D # ResNet_34模型
# from model50_new import ResNet_50_2D # ResNet_50模型
# 配置参数 - 根据您的需求调整
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
TRAIN_PATH = "/home/<USER>/1yaojy/pipei/train"  # 在gpu上的路径
VAL_PATH = "/home/<USER>/1yaojy/pipei/val"  # 添加验证集路径
# TRAIN_PATH = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\R-001-2\pipei\train"
# VAL_PATH = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\R-001-2\pipei\val"  # 添加验证集路径
BATCH_SIZE = 16  # 批大小
NUM_EPOCHS = 800  # 训练轮数
LEARNING_RATE = 0.001  # 学习率0.001，已经尝试0.0001 0.0005效果不如0.001
WEIGHT_DECAY = 1e-5  # 权重衰减1e-5，已经尝试1e-4或1e-6效果不如1e-5
SMAPE_EPS = 1e-6  # SMAPE计算中的小量
MODEL_SAVE_INTERVAL = 50  # 模型保存间隔
ATTENTION_FACTOR = 0.5  # 注意力因子0.5，

# 学习率调度器配置
SCHEDULER_TYPE = 'cosine'  # 选择调度器类型: 'cosine', 'step', 'plateau'
STEP_SIZE = 50  # StepLR的step_size参数
STEP_GAMMA = 0.5  # StepLR的gamma参数
PLATEAU_FACTOR = 0.5  # ReduceLROnPlateau的factor参数
PLATEAU_PATIENCE = 10  # ReduceLROnPlateau的patience参数
PLATEAU_MIN_LR = 1e-6  # ReduceLROnPlateau的最小学习率


def smape_scalar(pred, true, eps=SMAPE_EPS):
    """计算SMAPE指标"""
    pred = pred.view(-1)
    true = true.view(-1)
    return 1 - torch.abs(pred - true) / (torch.abs(pred) + torch.abs(true) + eps)


def validate(model, val_dl, device, mse_loss_fn, mae_loss_fn, alpha, beta):
    """验证模型性能"""
    model.eval()
    total_val_loss = 0.0
    total_val_smape = 0.0

    with torch.no_grad():
        for batch in val_dl:
            images, stiffness, _ = batch
            images = images.to(device)
            stiffness = stiffness.to(device)

            pred_stiffness = model(images)

            # 计算损失
            mse = mse_loss_fn(pred_stiffness, stiffness)
            mae = mae_loss_fn(pred_stiffness, stiffness)
            loss = alpha * mse + beta * mae

            # 计算SMAPE
            smape = smape_scalar(pred_stiffness, stiffness).mean()

            total_val_loss += loss.item()
            total_val_smape += smape.item()

    avg_val_loss = total_val_loss / len(val_dl)
    avg_val_smape = total_val_smape / len(val_dl)

    return avg_val_loss, avg_val_smape


def train():
    # 1. 准备数据集
    # # 基础变换 - 转换为张量
    # base_transform = transforms.Compose([
    #     transforms.Grayscale(num_output_channels=1),  # 确保为单通道
    # ])

    # 训练集
    train_dataset = CarDoorStiffnessDataset(
        root_dir=TRAIN_PATH,
        minite_dir="minite_points",
        thickness_dir="other_thickness",
        combination_dir="thickness_combination",
        data_file="extracted_data.npy",
        # transform=base_transform,
        # augment=True  # 训练时应用数据增强
    )

    # 验证集（不应用数据增强）
    val_dataset = CarDoorStiffnessDataset(
        root_dir=VAL_PATH,
        minite_dir="minite_points",
        thickness_dir="other_thickness",
        combination_dir="thickness_combination",
        data_file="extracted_data.npy",
        # transform=base_transform,
        # augment=False  # 验证时不应用数据增强
    )

    train_dl = DataLoader(
        train_dataset,
        batch_size=BATCH_SIZE,
        shuffle=True,
        num_workers=8,
        pin_memory=True
    )

    val_dl = DataLoader(
        val_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,  # 验证集不需要打乱
        num_workers=4,
        pin_memory=True
    )

    print(f"训练集大小: {len(train_dataset)} 样本")
    print(f"验证集大小: {len(val_dataset)} 样本")
    print(f"批次数量: 训练 {len(train_dl)} | 验证 {len(val_dl)}")

    # 2. 初始化模型
    model = ResNet_18_2D(
        in_channels=3,  # 三通道输入
        attention_factor=ATTENTION_FACTOR
    ).to(DEVICE)

    # 3. 设置优化器和学习率调度
    optim = torch.optim.Adam(
        model.parameters(),
        lr=LEARNING_RATE,
        weight_decay=WEIGHT_DECAY
    )

    # 根据配置选择学习率调度器
    if SCHEDULER_TYPE == 'cosine':
        scheduler = CosineAnnealingLR(optim, T_max=NUM_EPOCHS)  # 作用是什么
        print(f"使用CosineAnnealingLR调度器, T_max={NUM_EPOCHS}")
    elif SCHEDULER_TYPE == 'step':
        scheduler = StepLR(optim, step_size=STEP_SIZE, gamma=STEP_GAMMA)
        print(f"使用StepLR调度器, step_size={STEP_SIZE}, gamma={STEP_GAMMA}")
    elif SCHEDULER_TYPE == 'plateau':
        scheduler = ReduceLROnPlateau(
            optim,
            mode='min',
            factor=PLATEAU_FACTOR,
            patience=PLATEAU_PATIENCE,
            min_lr=PLATEAU_MIN_LR
        )
        print(
            f"使用ReduceLROnPlateau调度器, factor={PLATEAU_FACTOR}, patience={PLATEAU_PATIENCE}, min_lr={PLATEAU_MIN_LR}")
    else:
        raise ValueError(f"未知的调度器类型: {SCHEDULER_TYPE}")

    # 4. 定义损失函数
    mse_loss_fn = torch.nn.MSELoss()
    mae_loss_fn = torch.nn.L1Loss()
    alpha = 0.5  # MSE权重
    beta = 0.5  # MAE权重

    # 5. 创建保存目录
    os.makedirs("checkpoints", exist_ok=True)

    # 6. 训练日志
    train_loss_log = []
    train_smape_log = []
    val_loss_log = []
    val_smape_log = []
    lr_log = []
    best_val_smape = float('inf')

    print(f"开始训练... 设备: {DEVICE}")
    start_time = time.time()

    # 开始训练
    for epoch in range(1, NUM_EPOCHS + 1):
        model.train()
        epoch_train_loss = 0.0
        epoch_train_smape = 0.0

        # 训练批次
        for batch in train_dl:
            images, stiffness, _ = batch
            images = images.to(DEVICE)
            stiffness = stiffness.to(DEVICE)

            # 前向传播
            pred_stiffness = model(images)

            # 计算损失
            mse = mse_loss_fn(pred_stiffness, stiffness)
            mae = mae_loss_fn(pred_stiffness, stiffness)
            loss = alpha * mse + beta * mae

            # 计算SMAPE
            smape = smape_scalar(pred_stiffness, stiffness).mean()

            # 反向传播和优化
            optim.zero_grad()
            loss.backward()
            optim.step()

            # 更新日志
            epoch_train_loss += loss.item()
            epoch_train_smape += smape.item()

        # 计算训练平均指标
        avg_train_loss = epoch_train_loss / len(train_dl)  # 平均loss
        avg_train_smape = epoch_train_smape / len(train_dl) # 平均smape

        # 验证模型，调用validate函数，得到验证集的loss以及smape
        avg_val_loss, avg_val_smape = validate(
            model, val_dl, DEVICE, mse_loss_fn, mae_loss_fn, alpha, beta
        )

        # 记录学习率
        current_lr = optim.param_groups[0]['lr']
        lr_log.append(current_lr)

        # 记录日志
        train_loss_log.append(avg_train_loss)
        train_smape_log.append(avg_train_smape)
        val_loss_log.append(avg_val_loss)
        val_smape_log.append(avg_val_smape)

        # 更新学习率调度器
        if SCHEDULER_TYPE == 'plateau':
            # 对于Plateau调度器，使用验证损失作为指标
            scheduler.step(avg_val_loss)
        elif SCHEDULER_TYPE in ['cosine', 'step']:
            # 对于Cosine和Step调度器，按epoch更新
            scheduler.step()

        # 打印进度
        print(f"Epoch {epoch:03d}/{NUM_EPOCHS} | "
              f"Train Loss: {avg_train_loss:.4e} | "
              f"Train SMAPE: {avg_train_smape:.4f} | "
              f"Val Loss: {avg_val_loss:.4e} | "
              f"Val SMAPE: {avg_val_smape:.4f} | "
              f"LR: {current_lr:.2e}")

        # 定期保存模型,每50次保存一回模型
        if epoch % MODEL_SAVE_INTERVAL == 0 or epoch == NUM_EPOCHS:
            save_path = f"checkpoints/model_epoch{epoch}.pth"
            torch.save(model.state_dict(), save_path)
            print(f"模型保存至: {save_path}")

        # 保存最佳模型（基于验证SMAPE）
        if avg_val_smape < best_val_smape:
            best_val_smape = avg_val_smape
            torch.save(model.state_dict(), "checkpoints/best_model.pth")
            print(f"新最佳验证SMAPE: {best_val_smape:.4f} - 模型已保存")

    # 7. 训练完成
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\n训练完成! 总耗时: {total_time:.2f}秒 ({total_time / 60:.2f}分钟)")

    # 保存最终模型
    torch.save(model.state_dict(), "model_final.pth")
    print("最终模型保存至: model_final.pth")

    # 8. 绘制损失曲线和学习率曲线
    plt.figure(figsize=(18, 12))

    # 损失曲线
    plt.subplot(2, 2, 1)
    plt.plot(train_loss_log, label="train_loss")
    plt.plot(val_loss_log, label="validation_")
    plt.xlabel("Epoch")
    plt.ylabel("loss")
    plt.title("train and val loss curve")
    plt.grid(True)
    plt.legend()

    # SMAPE曲线
    plt.subplot(2, 2, 2)
    plt.plot(train_smape_log, label="train_SMAPE")
    plt.plot(val_smape_log, label="val_SMAPE")
    plt.xlabel("Epoch")
    plt.ylabel("SMAPE")
    plt.title("train and val SMAPE curve")
    plt.grid(True)
    plt.legend()

    # 学习率曲线
    plt.subplot(2, 2, 3)
    plt.plot(lr_log, label="learning rate", color="green")
    plt.xlabel("Epoch")
    plt.ylabel("learning rate")
    plt.yscale('log')  # 对数尺度更易观察变化
    plt.title("learning rate curve")
    plt.grid(True)
    plt.legend()

    # 验证损失与学习率关系
    plt.subplot(2, 2, 4)
    ax1 = plt.gca()
    ax2 = ax1.twinx()

    ax1.plot(val_loss_log, 'b-', label="validation_loss")
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('validation_loss', color='b')
    ax1.tick_params('y', colors='b')

    ax2.plot(lr_log, 'r-', label="learning rate")
    ax2.set_ylabel('learning rate', color='r')
    ax2.tick_params('y', colors='r')
    ax2.set_yscale('log')

    plt.title("val_los and learning rate")
    plt.grid(True)

    plt.tight_layout()
    plt.savefig("training_metrics.png", dpi=200)
    print("训练指标图保存至: training_metrics.png")

    # 保存日志数据
    log_data = {
        'train_loss': train_loss_log,
        'train_smape': train_smape_log,
        'val_loss': val_loss_log,
        'val_smape': val_smape_log,
        'lr': lr_log,
        'config': {
            'scheduler_type': SCHEDULER_TYPE,
            'learning_rate': LEARNING_RATE,
            'attention_factor': ATTENTION_FACTOR,
            'batch_size': BATCH_SIZE,
            'num_epochs': NUM_EPOCHS
        }
    }
    torch.save(log_data, "training_log.pth")
    print("训练日志保存至: training_log.pth")


if __name__ == "__main__":
    train()