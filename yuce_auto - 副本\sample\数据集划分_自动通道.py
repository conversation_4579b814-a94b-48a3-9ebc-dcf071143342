import os
import re
import shutil
import random
import numpy as np
from tqdm import tqdm
"""'
自动获取所有通道文件夹并处理文件名匹配问题
"""

def split_dataset(
        root_dir,
        train_ratio=0.8,
        output_dir=None,
        exclude_dirs=["train", "val"]  # 需要排除的目录
):
    """
    将数据集划分为训练集和验证集，并复制图片到相应目录
    参数:
        root_dir: 数据集根目录
        train_ratio: 训练集比例
        output_dir: 输出目录，默认为root_dir下创建train和val子目录
        exclude_dirs: 需要排除的目录列表
    """
    # 1. 自动获取所有通道文件夹-这步最终要的是all_channels列表储存所有文件夹的名字
    all_channels = []
    for item in os.listdir(root_dir):  # os.listdir(root_dir)获取路径下所有文件夹的名字列表
        item_path = os.path.join(root_dir, item)  # 组合路径，从而进入每一个文件夹
        if os.path.isdir(item_path) and item not in exclude_dirs:
            # 检查文件夹中是否有PNG文件
            png_files = [f for f in os.listdir(item_path) if f.lower().endswith('.png')]
            if png_files:
                all_channels.append(item)   # 将路径下所有文件夹的名字加到all_channels列表

    if not all_channels:
        raise ValueError(f"在 {root_dir} 中未找到有效的通道文件夹")

    print(f"找到 {len(all_channels)} 个通道文件夹:")
    for channel in all_channels:
        print(f"  - {channel}")

    # 2. 获取所有文件的文件名集合——要的是all_files：储存这每个灰度图的名字
    # 使用正则表达式匹配文件名格式: R_XXX_XXXXX_components_multi.png
    pattern = re.compile(r'F-[a-zA-Z0-9]{3}-[a-zA-Z0-9]{5}-components_multi\.png', re.IGNORECASE)

    # 找出所有通道文件夹中共同存在的文件（交集）
    common_files = None
    for channel in all_channels:  # 进入每个通道文件夹里
        channel_path = os.path.join(root_dir, channel)
        files_in_channel = [f for f in os.listdir(channel_path)  # 比如获取minite_points文件夹下的所有图片名字，保存列表格式
                            if pattern.match(f) and f.lower().endswith('.png')]

        if common_files is None:
            common_files = set(files_in_channel)
        else:
            common_files = common_files.intersection(files_in_channel)

    if not common_files:  # common_files储存的是共同的PNG文件
        raise ValueError("所有通道文件夹中没有共同的PNG文件")

    all_files = sorted(list(common_files))  # 排序以便可重复
    print(f"\n找到 {len(all_files)} 个共同文件 (文件名示例: {all_files[0] if all_files else '无'})")


    # 3. 创建输出目录结构-创建"train"文件夹，其里面储存all_channels所有通道文件夹的名字。
    if output_dir is None:
        output_dir = root_dir

    train_dirs = {channel: os.path.join(output_dir, "train", channel) for channel in all_channels}
    val_dirs = {channel: os.path.join(output_dir, "val", channel) for channel in all_channels}
    print("train_dirs",train_dirs)
    print("train_dirs.values", train_dirs.values())
    for dir_path in list(train_dirs.values()) + list(val_dirs.values()):
        os.makedirs(dir_path, exist_ok=True)

    # 4. 随机打乱文件列表-打乱的是灰度图图片名字的列表
    random.shuffle(all_files)

    # 5. 划分数据集
    split_idx = int(len(all_files) * train_ratio)
    train_files = all_files[:split_idx] # 是训练集中所有图片名字列表
    val_files = all_files[split_idx:]

    print(f"\n总样本数: {len(all_files)}")
    print(f"训练集大小: {len(train_files)}")
    print(f"训练集train_files: {train_files}")
    print(f"验证集大小: {len(val_files)}")

    # 6. 定义复制函数：file_list是训练集图片名字，src_paths是all_channels列表储存所有文件夹的名字，
    def copy_files(file_list, src_paths, dst_paths):
        for file in tqdm(file_list, desc="复制文件"):  # file是某个图片的名字
            for channel in src_paths:   # 某一个文件夹如minite_points
                src = os.path.join(root_dir, channel, file)  # 比如pipei/minite_points中图片a的路径
                dst = os.path.join(dst_paths[channel], file)  # pipei/train/minite_points中图片a的路径
                if os.path.exists(src):
                    shutil.copy2(src, dst)  # 将pipei/minite_points中图片复制pipei/train/minite_points中
                else:
                    print(f"警告: 文件 {src} 不存在")

    # 7. 复制训练集图片
    print("\n复制训练集图片...")
    copy_files(train_files, all_channels, train_dirs)

    # 8. 复制验证集图片
    print("\n复制验证集图片...")
    copy_files(val_files, all_channels, val_dirs)

    # 9. 创建索引文件
    with open(os.path.join(output_dir, "train_files.txt"), "w") as f:
        f.write("\n".join(train_files))
    with open(os.path.join(output_dir, "val_files.txt"), "w") as f:
        f.write("\n".join(val_files))


    print("\n数据集划分完成!")
    print(f"训练集路径: {os.path.join(output_dir, 'train')}")
    print(f"验证集路径: {os.path.join(output_dir, 'val')}")


if __name__ == "__main__":
    # 配置参数 - 根据您的实际路径修改
    ROOT_DIR = r"D:\Aprojecdtdoor\Dataset_Construction\code-v2\yuce_xiu_Varietyoftypessample\sample\all"
    TRAIN_RATIO = 0.8

    # 可以排除不需要的文件夹
    EXCLUDE_DIRS = ["train", "val"]

    split_dataset(
        root_dir=ROOT_DIR,
        train_ratio=TRAIN_RATIO,
        exclude_dirs=EXCLUDE_DIRS
    )
