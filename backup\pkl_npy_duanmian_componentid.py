import os
import re
import pickle
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.spatial import KDTree
import matplotlib
# --------------将pkl转为体素化，为实现车门外腰线工况的预测模型搭建，增加要进行断面切片的板件通道,为确定断面位置加入输出工况点坐标,增加自动处理不同车门，将组件id
#通过txt配置文件进行赋值------------

matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 或 'Microsoft YaHei'
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号无法显示的问题
def load_door_config(config_file_path):
    config_dict = {}
    current_door_type = None

    with open(config_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            # 匹配车门类型 [001]
            if line.startswith('[') and line.endswith(']'):
                current_door_type = line[1:-1]
                config_dict[current_door_type] = {}
                continue

            if current_door_type is None:
                continue

            # 解析配置项
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()

                # 特殊处理none值
                if value.lower() == 'none':
                    config_dict[current_door_type][key] = None
                # 处理ID列表
                elif key in ['loading_node_ids', 'weld_comp_ids', 'glue_comp_ids',
                             'INNER_COMP_IDS', 'MARKED_COMP_IDS']:
                    config_dict[current_door_type][key] = set(int(x) for x in value.split(','))
                # 处理单个ID
                elif key in ['OUTER_COMP_ID', 'REINFORCE_A_COMP_ID', 'REINFORCE_B_COMP_ID']:
                    config_dict[current_door_type][key] = int(value)
                else:
                    config_dict[current_door_type][key] = value
    print('config_dict', config_dict)
    return config_dict
# 从文件名中提取车门类型
def extract_door_type(filename):
    # 支持两种文件名格式: R_024_00013_components.pkl 或 R-024-00013-components.pkl
    match = re.search(r'[F_\-](?P<door_type>\d{3})[_\-]', filename)
    if match:
        return match.group('door_type')
    return None
# ------------------------------------------------------------------------
# 1. 从 .pkl 中加载 full_data 而不只是节点坐标（保持不变）
def load_full_data_from_pickle(pkl_file_path):
    with open(pkl_file_path, 'rb') as f:
        full_data = pickle.load(f)
    return full_data

# ------------------------------------------------------------------------
# 2. annotate_component_per_node：按组件给每个节点打标签（保持不变）
def annotate_component_per_node(full_data, comp_id, config):
    comp_dict = full_data['components'][comp_id]
    node_dict = comp_dict.get('nodes', {})
    elements = comp_dict.get('elements', [])
    prop = comp_dict.get('property', None)

    # 1) material_id
    if prop and 'mid' in prop and prop['mid'] is not None:
        material_id = prop['mid']
    else:
        material_id = 0

    # 2) thickness
    if prop and prop.get('type', '') == 'PSHELL' and prop.get('thickness') is not None:
        thickness_value = prop['thickness']
    else:
        thickness_value = 0.0

    # 3) 强制标记焊点/胶
    weld_comp_ids = config.get('weld_comp_ids', set())
    glue_comp_ids = config.get('glue_comp_ids', set())

    force_all_weld = (comp_id in weld_comp_ids)
    force_all_glue = (comp_id in glue_comp_ids)

    # 4) 根据元素类型划分
    entity_node_ids = set()
    rbe2_node_ids   = set()
    weld_node_ids   = set()
    glue_node_ids   = set()

    if not force_all_weld and not force_all_glue:
        for elem in elements:
            etype = elem.get('element_type', '')
            nids  = elem.get('node_ids', [])
            if etype in {'CHEXA', 'CPENTA', 'CTRIA3', 'CQUAD4'}:
                entity_node_ids.update(nids)
            elif etype.startswith('RBE2'):
                ind = elem.get('independent_node')
                if ind is not None:
                    rbe2_node_ids.add(ind)
                dep = elem.get('dependent_nodes', [])
                if isinstance(dep, (int, np.integer)):
                    rbe2_node_ids.add(dep)
                else:
                    rbe2_node_ids.update(dep)

    # 5) 给每个节点打标签
    annotated = []
    for nid, (x, y, z) in node_dict.items():
        is_entity = 1 if (nid in entity_node_ids) else 0
        is_glue   = 1 if (nid in glue_node_ids) else 0
        is_weld   = 1 if (nid in weld_node_ids) else 0
        is_rbe2   = 1 if (nid in rbe2_node_ids) else 0

        if force_all_weld:
            is_weld = 1
        if force_all_glue:
            is_glue = 1

        annotated.append((nid, x, y, z,
                          is_entity, material_id, thickness_value,
                          is_glue, is_weld, is_rbe2, comp_id))  # 添加comp_id
    return annotated


# # ------------------------------------------------------------------------
from scipy.spatial import KDTree

def calculate_distance_difference_projected_y(outer_points, inner_points,
                                              voxel_coords, global_min, res,
                                              k=3, fill_value=0.0):
    """
    用 (X,Z) 投影求内外板 Y 差值，Y 为垂直方向。
    对空缺位置做 KNN 加权平均插值，避免空白。
    """
    # --- 1. 构建投影点集及对应 Y 值 ---
    outer_map, outer_coords = {}, []
    for x, y, z in outer_points:
        ix = int((x - global_min[0])//res)
        iz = int((z - global_min[2])//res)
        key = (ix, iz)
        if key in outer_map:
            outer_map[key] = max(outer_map[key], y)
        else:
            outer_map[key] = y
            outer_coords.append([ix, iz])
    outer_coords = np.array(outer_coords)
    outer_vals   = np.array([outer_map[tuple(c)] for c in outer_coords])

    inner_map, inner_coords = {}, []
    for x, y, z in inner_points:
        ix = int((x - global_min[0])//res)
        iz = int((z - global_min[2])//res)
        key = (ix, iz)
        if key in inner_map:
            inner_map[key] = min(inner_map[key], y)
        else:
            inner_map[key] = y
            inner_coords.append([ix, iz])
    inner_coords = np.array(inner_coords)
    inner_vals   = np.array([inner_map[tuple(c)] for c in inner_coords])

    # 若缺少任一板，全部归 0
    if len(outer_coords)==0 or len(inner_coords)==0:
        return np.zeros(len(voxel_coords), dtype=float)

    # --- 2. 构建 KDTree ---
    outer_tree = KDTree(outer_coords)
    inner_tree = KDTree(inner_coords)

    # --- 3. 针对每个投影格做 KNN 加权平均插值（填补空缺） ---
    proj = np.array([[i, k] for i,_,k in voxel_coords])
    # 外板
    d_o, idx_o = outer_tree.query(proj, k=k, eps=0, p=2, distance_upper_bound=np.inf)
    # 如果返回的维度为 (N,), 强制变成 (N,1)
    if d_o.ndim == 1:
        d_o = d_o[:,None]; idx_o = idx_o[:,None]
    w_o = 1/(d_o + 1e-6)
    w_o /= np.sum(w_o, axis=1, keepdims=True)
    y_o = np.sum(outer_vals[idx_o] * w_o, axis=1)

    # 内板
    d_i, idx_i = inner_tree.query(proj, k=k)
    if d_i.ndim == 1:
        d_i = d_i[:,None]; idx_i = idx_i[:,None]
    w_i = 1/(d_i + 1e-6)
    w_i /= np.sum(w_i, axis=1, keepdims=True)
    y_i = np.sum(inner_vals[idx_i] * w_i, axis=1)

    # --- 4. 计算差值并归一化 ---
    diff = np.abs(y_o - y_i)

    # 百分位裁剪（去极值影响）
    p1, p99 = np.percentile(diff, [1, 99])
    diff = np.clip(diff, p1, p99)
    diff = (diff - p1) / (p99 - p1 + 1e-8)

    return diff

# ------------------------------------------------------------------------
# 4. batch_process（保持原来逻辑，只需保证 visualize_all_channels 已修改）
def batch_process(input_directory, output_directory, config_file_path, visualize=False):
    # 加载车门配置文件
    door_configs = load_door_config(config_file_path)  # 是所有车门与对应组件id的对应
    os.makedirs(output_directory, exist_ok=True)
    pkl_files = [f for f in os.listdir(input_directory) if f.endswith('.pkl')]
    if not pkl_files:
        raise RuntimeError("指定目录下没有任何 .pkl 文件！")

    # 1) 收集所有模型中的所有节点坐标，算 global_min/global_max,以计算包装盒大小
    all_nodes = []
    for pkl_file in pkl_files:
        full_data = load_full_data_from_pickle(os.path.join(input_directory, pkl_file))
        for comp_id, coords in full_data['geometry'].items():
            all_nodes.append(np.array(coords))
    if not all_nodes:
        raise RuntimeError("未在任何 .pkl 文件中找到节点坐标！")
    all_nodes_stacked = np.vstack(all_nodes)
    global_min = np.min(all_nodes_stacked, axis=0)
    global_max = np.max(all_nodes_stacked, axis=0)

    # 1.1) 计算真实范围
    # padding = 5.0
    ranges = (global_max - global_min)

    # 1.2) 固定分辨率 5 mm，动态算 nx, ny, nz
    res = 5
    nx = int(np.ceil(ranges[0] / res))
    ny = int(np.ceil(ranges[1] / res))
    nz = int(np.ceil(ranges[2] / res))
    print(f"▶ 全局体素大小: ({nx}, {ny}, {nz}), 分辨率={res} mm")
    # 2) 对每个模型文件进行处理,对每个模型文件，生成一个全局多通道体素并保存,
    for pkl_file in pkl_files:
        base_name = os.path.splitext(pkl_file)[0]
        print(f"\n▶ 正在处理模型：{base_name}")
        # 提取车类型并获取对应配置
        door_type = extract_door_type(pkl_file)  # 对于"R_024_00013_components.pkl" door_type = 024
        if not door_type:
            print(f"  ⚠ 警告: 无法从文件名 {pkl_file} 中提取车门类型，跳过处理")
            continue

        if door_type not in door_configs:
            print(f"  ⚠ 警告: 找不到车门类型 {door_type} 的配置，跳过处理")
            continue

        config = door_configs[door_type]  # config是{'loading_node_ids': {42232, 42227...}是config字典中024的部分
        print(f"  → 使用车门类型 {door_type} 的配置")

        full_data = load_full_data_from_pickle(os.path.join(input_directory, pkl_file))
        num_components = len(full_data['components'])
        print(f"  → 读取到 {num_components} 个组件。")
        # ========================================================
        # 新增：查找加载点（工况点）
        # ========================================================
        # 1. 获取全局节点坐标字典
        global_node_coords = full_data.get('global_node_coordinates', None)

        # 2. 指定要查找的节点ID
        loading_node_ids = config.get('loading_node_ids', [])
        found_nodes = []
        missing_nodes = []
        loading_point_voxel = (-1, -1, -1)  # 默认无效值

        # 3. 检查全局节点坐标是否存在
        if global_node_coords is not None:
            print(f"  → 找到全局节点坐标字典，包含 {len(global_node_coords)} 个节点")

            # 4. 查找节点并收集坐标
            for nid in loading_node_ids:
                if nid in global_node_coords:
                    found_nodes.append(global_node_coords[nid])
                else:
                    missing_nodes.append(nid)

            # 5. 计算平均坐标（如果找到所有节点）
            if len(found_nodes) == 4:
                # 计算四个节点的平均坐标
                avg_coord = np.mean(found_nodes, axis=0)
                print(f"  ✅ 找到所有加载点节点，平均坐标: {avg_coord}")

                # 6. 转换为体素索引
                i = int(np.floor((avg_coord[0] - global_min[0]) / res))
                j = int(np.floor((avg_coord[1] - global_min[1]) / res))
                k = int(np.floor((avg_coord[2] - global_min[2]) / res))

                # 确保索引在体素网格范围内
                i = max(0, min(nx - 1, i))
                j = max(0, min(ny - 1, j))
                k = max(0, min(nz - 1, k))

                loading_point_voxel = (i, j, k)
                print(f"  ✅ 加载点体素索引: {loading_point_voxel}")
            else:
                print(f"  ⚠ 警告: 缺失节点 {missing_nodes}，无法计算加载点")
        else:
            print(f"  ⚠ 警告: 未找到全局节点坐标字典，无法计算加载点")

        # 初始化多通道体素：(nx, ny, nz, 13)
        voxel_grid = np.zeros((nx, ny, nz, 13), dtype=np.float32)
        entity_comp = np.zeros((nx, ny, nz), dtype=np.int32)
        # 存储内外板坐标点
        outer_points = []
        inner_points = []
        # 分别存储不同板的厚度
        outer_thickness_dict = {}
        inner_thickness_dict = {}
        other_thickness_dict = {}

        # 定义组件ID
        OUTER_COMP_ID = config.get('OUTER_COMP_ID', 0) # 外板的，调用config字典中'OUTER_COMP_ID'部分
        INNER_COMP_IDS = config.get('INNER_COMP_IDS', set())  # 内板的
        # 新增：定义腰线加强板组件ID
        REINFORCE_A_COMP_ID = config.get('REINFORCE_A_COMP_ID', 0)  # 腰线加强板A
        REINFORCE_B_COMP_ID = config.get('REINFORCE_B_COMP_ID', 0)  # 腰线加强板B
        # 定义需要标记的组件集合-腰线工况附近断面
        MARKED_COMP_IDS = config.get('MARKED_COMP_IDS', set())
        print(f"  → 使用配置: OUTER_COMP_ID={OUTER_COMP_ID}, INNER_COMP_IDS={INNER_COMP_IDS}")
        print(f"  → REINFORCE_A_COMP_ID={REINFORCE_A_COMP_ID}, REINFORCE_B_COMP_ID={REINFORCE_B_COMP_ID}")
        print(f"  → MARKED_COMP_IDS={MARKED_COMP_IDS}")
        for comp_id in full_data['components'].keys():
            annotated = annotate_component_per_node(full_data, comp_id, config)
            for (_, x, y, z, is_entity, mat_id, thk_val, is_glue, is_weld, is_rbe2, comp_id_val) in annotated:
                i = int(np.floor((x - global_min[0]) / res))
                j = int(np.floor((y - global_min[1]) / res))
                k = int(np.floor((z - global_min[2]) / res))
                if not (0 <= i < nx and 0 <= j < ny and 0 <= k < nz):
                    continue
                # 通道0：外板 (comp_id=72)
                if comp_id_val == OUTER_COMP_ID and is_entity:
                    voxel_grid[i, j, k, 0] = 1.0  # 0通道储存是否是外板
                    entity_comp[i, j, k] = comp_id_val  # 将外板id赋予entity_comp
                    outer_points.append([x, y, z])  # 将所以外板包括的节点储存，储存外板点云数据
                    # 记录外板厚度，在annotated数组中寻找xyz坐标以及comp_id对应下的厚度
                    key = (i, j, k)
                    if thk_val > 0:
                        if key in outer_thickness_dict:
                            outer_thickness_dict[key].add(round(thk_val, 6))
                        else:
                            outer_thickness_dict[key] = {round(thk_val, 6)}
                # 通道1：内板 (comp_id=73或74)
                elif comp_id_val in INNER_COMP_IDS and is_entity:
                    voxel_grid[i, j, k, 1] = 1.0     #
                    entity_comp[i, j, k] = comp_id_val
                    inner_points.append([x, y, z])  # 获取内板在三维真实空间点云
                    # 记录内板厚度
                    key = (i, j, k)  # 在某个体素索引中，即某个体素块里
                    if thk_val > 0:
                        if key in inner_thickness_dict:  # 如果inner_thickness_dict字典中已经有了在key体素块下的厚度
                            inner_thickness_dict[key].add(round(thk_val, 6)) # 将厚度加入到集合（set）中
                        else:
                            inner_thickness_dict[key] = {round(thk_val, 6)}
                # 通道2：中间板 (其他实体组件)
                elif is_entity and comp_id_val != OUTER_COMP_ID and comp_id_val not in INNER_COMP_IDS:
                    voxel_grid[i, j, k, 2] = 1.0
                    entity_comp[i, j, k] = comp_id_val
                    # 记录其他板厚度
                    key = (i, j, k)
                    if thk_val > 0:
                        if key in other_thickness_dict:
                            other_thickness_dict[key].add(round(thk_val, 6)) # round() 方法返回浮点数x的四舍五入值。
                        else:
                            other_thickness_dict[key] = {round(thk_val, 6)}
                # 新增：通道10 腰线加强板A (comp_id==75)
                if comp_id_val == REINFORCE_A_COMP_ID and is_entity:
                    voxel_grid[i, j, k, 10] = 1.0

                # 新增：通道11 腰线加强板B (comp_id==76)
                if comp_id_val == REINFORCE_B_COMP_ID and is_entity:
                    voxel_grid[i, j, k,11] = 1.0
                # 新增：腰线断面相关截面形状
                if comp_id_val in MARKED_COMP_IDS and is_entity:
                    voxel_grid[i, j, k, 12] = 1.0  # 第12通道标记这些组件
                if is_glue:
                    voxel_grid[i, j, k, 3] = 1.0
                if is_weld:
                    voxel_grid[i, j, k, 4] = 1.0
                if is_rbe2:
                    voxel_grid[i, j, k, 5] = 1.0

        # 将厚度值写入各自的通道
        # 通道6：外板厚度
        for (i, j, k), thk_set in outer_thickness_dict.items():
            voxel_grid[i, j, k, 6] = float(sum(thk_set))
        # 通道7：内板厚度
        for (i, j, k), thk_set in inner_thickness_dict.items():
            voxel_grid[i, j, k, 7] = float(sum(thk_set))
        # 通道8：其他板厚度
        for (i, j, k), thk_set in other_thickness_dict.items():
            voxel_grid[i, j, k, 8] = float(sum(thk_set))
        # 计算通道9：内外板距离差值
        if outer_points and inner_points:
            # 获取所有非零体素的坐标
            non_zero_mask = np.any(voxel_grid[..., :9] > 0, axis=-1)
            non_zero_coords = np.argwhere(non_zero_mask)

            # 计算距离差值
            distance_diff = calculate_distance_difference_projected_y(
                np.array(outer_points),
                np.array(inner_points),
                non_zero_coords,
                global_min,
                res
            )
            # 只给板面体素（外板或内板）赋值，避免窗框等其他结构影响
            panel_mask = (voxel_grid[..., 0] > 0) | (voxel_grid[..., 1] > 0)
            for idx, (i, j, k) in enumerate(non_zero_coords):
                if panel_mask[i, j, k]:
                    voxel_grid[i, j, k, 9] = distance_diff[idx]

        # 5) 保存为 .npz（压缩格式），后缀加 “_multi”
        save_path = os.path.join(output_directory, f"{base_name}_multi.npz")
        # 修改：将加载点坐标也保存到NPZ文件中
        np.savez_compressed(
            save_path,
            voxel=voxel_grid,
            entity_comp=entity_comp,
            loading_point=np.array(loading_point_voxel)  # 新增：保存加载点坐标
        )
        print(f"  ✅ 模型 {base_name} 已保存多通道体素 (shape={voxel_grid.shape}) 至\n     {save_path}")
        print(f"  ✅ 加载点体素索引已保存: {loading_point_voxel}")

        if visualize:
            # 创建可视化输出目录
            vis_dir = os.path.join(output_directory, "visualizations")
            os.makedirs(vis_dir, exist_ok=True)
            # 各通道分开可视化
            output_path_separate = os.path.join(vis_dir, f"{base_name}_all_channels.png")  # 储存图片到visualizations
            visualize_all_channels(voxel_grid, entity_comp,
                                   output_path=output_path_separate,
                                   title=f"Voxel - {base_name}")

            # 合并可视化
            output_path_combined = os.path.join(vis_dir, f"{base_name}_combined.png")
            visualize_all_channels_combined(voxel_grid, entity_comp,
                                            output_path=output_path_combined,
                                            title=f"Combined Channels - {base_name}")
    print("\n所有模型多通道体素化完成！")

# ------------------------------------------------------------------------
# 5. visualize_all_channels：可视化8个通道
def visualize_all_channels(voxel_data, entity_comp, output_path=None, title=None):
    """
    可视化 12 通道体素数据，每个通道放在一个子图里
    """
    n_channels = voxel_data.shape[3]
    channel_names = [
        "外板", "内板", "中间板",
        "胶", "焊点", "RBE2",
        "外板厚度", "内板厚度", "其他板厚度",
        "内外板距离差值",
        "腰线加强板A", "腰线加强板B",
        "标记组件(72,74,75,76,84)"  # 新增通道名称
    ]

    # 动态计算行和列数
    n_cols = 4  # 每行最多4个子图
    n_rows = (n_channels + n_cols - 1) // n_cols  # 计算需要的行数

    # 创建更大的图形
    fig = plt.figure(figsize=(6 * n_cols, 6 * n_rows))

    for ch in range(n_channels):
        ax = fig.add_subplot(n_rows, n_cols, ch + 1, projection='3d')
        channel_data = voxel_data[..., ch]
        x, y, z = np.where(channel_data > 0)

        if len(x) == 0:
            ax.set_title(f"{channel_names[ch]} (空)")
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.set_zlim(0, 1)
            ax.set_box_aspect([1, 1, 1])
            ax.set_xticks([])
            ax.set_yticks([])
            ax.set_zticks([])
            continue


        # 厚度和距离差值通道使用热力图
        if ch in [6, 7, 8, 9]:  # 厚度通道和距离差值通道
            values = channel_data[x, y, z]
            sc = ax.scatter(x, y, z, c=values, cmap='hot', s=5, alpha=0.7)
            plt.colorbar(sc, ax=ax, shrink=0.6, label='值')
        # 其他通道用固定颜色
        else:
            colors = ['blue', 'green', 'orange', 'red', 'cyan', 'magenta',
                      'yellow', 'purple', 'pink', 'brown', 'lime', 'navy','gold']
            color_idx = min(ch, len(colors) - 1)
            ax.scatter(x, y, z, c=colors[color_idx], s=5, alpha=0.7)

        # 设置坐标轴范围
        min_x, max_x = x.min(), x.max()
        min_y, max_y = y.min(), y.max()
        min_z, max_z = z.min(), z.max()
        overall_min = min(min_x, min_y, min_z)
        overall_max = max(max_x, max_y, max_z)
        ax.set_xlim(overall_min, overall_max)
        ax.set_ylim(overall_min, overall_max)
        ax.set_zlim(overall_min, overall_max)
        ax.set_box_aspect([1, 1, 1])

        ax.set_title(channel_names[ch], fontsize=14)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')

    if title:
        plt.suptitle(title, fontsize=24, y=0.98)
    plt.tight_layout(pad=3.0)

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"多通道可视化已保存至: {output_path}")

    # plt.show() # 关闭图形，不显示窗口
    plt.close(fig)

# ------------------------------------------------------------------------
# 6. visualize_all_channels_combined：组合可视化
def visualize_all_channels_combined(voxel_data, entity_comp, output_path=None, title=None):
    from matplotlib import colors

    channel_styles = {
        0: {'type': 'fixed', 'color': 'blue', 'label': '外板'},  # 外板
        1: {'type': 'fixed', 'color': 'green', 'label': '内板'},  # 内板
        2: {'type': 'fixed', 'color': 'orange', 'label': '中间板'},  # 中间板
        3: {'type': 'fixed', 'color': 'red', 'label': '胶'},  # 胶
        4: {'type': 'fixed', 'color': 'cyan', 'label': '焊点'},  # 焊点
        5: {'type': 'fixed', 'color': 'magenta', 'label': 'RBE2'},  # RBE2
        6: {'type': 'colormap', 'cmap': 'hot', 'label': '外板厚度'},  # 外板厚度
        7: {'type': 'colormap', 'cmap': 'hot', 'label': '内板厚度'},  # 内板厚度
        8: {'type': 'colormap', 'cmap': 'hot', 'label': '其他板厚度'},  # 其他板厚度

    }

    fig = plt.figure(figsize=(14, 12))
    ax = fig.add_subplot(111, projection='3d')

    nx, ny, nz, n_channels = voxel_data.shape
    scatter_handles = []

    # 绘制所有相关通道（不包括腰线加强板）
    for ch in [0, 1, 2, 3, 4, 5, 6, 7, 8]:  # 只保留基础通道
        if ch not in channel_styles:
            continue

        channel_data = voxel_data[..., ch]
        x, y, z = np.where(channel_data > 0)
        if len(x) == 0:
            continue

        style = channel_styles[ch]
        if style['type'] == 'fixed':
            sc = ax.scatter(x, y, z, c=style['color'], s=5, alpha=0.6, label=style['label'])
            scatter_handles.append(sc)
        else:
            values = channel_data[x, y, z]
            cmap = plt.get_cmap(style['cmap'])
            sc = ax.scatter(x, y, z, c=values, cmap=cmap, s=5, alpha=0.6, label=style['label'])
            scatter_handles.append(sc)

    # 设置坐标轴
    ax.set_xlabel('X', fontsize=12)
    ax.set_ylabel('Y', fontsize=12)
    ax.set_zlabel('Z', fontsize=12)

    # 坐标范围一致化
    all_pts = np.vstack([
        np.column_stack(np.where(voxel_data[..., ch] > 0))
        for ch in channel_styles.keys()
        if np.any(voxel_data[..., ch] > 0)
    ])
    if all_pts.size > 0:
        min_x, max_x = int(all_pts[:, 0].min()), int(all_pts[:, 0].max())
        min_y, max_y = int(all_pts[:, 1].min()), int(all_pts[:, 1].max())
        min_z, max_z = int(all_pts[:, 2].min()), int(all_pts[:, 2].max())
        overall_min = min(min_x, min_y, min_z)
        overall_max = max(max_x, max_y, max_z)
        ax.set_xlim(overall_min, overall_max)
        ax.set_ylim(overall_min, overall_max)
        ax.set_zlim(overall_min, overall_max)
    ax.set_box_aspect([1, 1, 1])

    # 图例
    ax.legend(loc='upper right', fontsize=10)

    # 给厚度通道加 colorbar
    for sh in scatter_handles:
        if sh.get_label() in ['外板厚度', '内板厚度', '其他板厚度']:
            plt.colorbar(sh, ax=ax, shrink=0.6, label=sh.get_label())

    if title:
        plt.title(title, fontsize=16, pad=20)

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"已保存合并可视化图到: {output_path}")

    # plt.show()
    plt.close(fig)


# ------------------------------------------------------------------------
# 7. 运行示例
if __name__ == "__main__":
    input_directory  = r"D:\Aprojecdtdoor\Dataset_Construction\data_pkl\F-015-V1"
    output_directory = r"D:\Aprojecdtdoor\Dataset_Construction\data_npy\F-015-V1"
    config_file_path = r"D:\Aprojecdtdoor\Dataset_Construction\data_npy\door_config.txt" # 配置文件路径
    os.makedirs(output_directory, exist_ok=True)

    # 批量生成多通道体素，并弹窗可视化
    batch_process(input_directory, output_directory, config_file_path, visualize=True)

"""R-001-npy/
    ├── model1_multi.npz
    ├── model2_multi.npz
    ├── ...
    └── visualizations/
        ├── model1_all_channels.png
        ├── model1_combined.png
        ├── model2_all_channels.png
        ├── model2_combined.png
        └── ..."""
