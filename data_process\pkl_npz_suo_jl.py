import os
import re
import pickle
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from scipy.spatial import KDTree
import matplotlib
# --------------修改已经生成的npz文件，添加新的通道15 16来存储上下铰链加强板组件的ID------------
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 或 'Microsoft YaHei'
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号无法显示的问题
def load_door_config(config_file_path):
    config_dict = {}
    current_door_type = None

    with open(config_file_path, 'r', encoding='utf-8') as f:
        for line in f:  # 每一行
            line = line.strip()  # 移除字符串两端的空白字符
            if not line or line.startswith('#'):
                continue

            # 匹配车门类型 [001]
            if line.startswith('[') and line.endswith(']'):
                current_door_type = line[1:-1]
                config_dict[current_door_type] = {}
                continue

            if current_door_type is None:
                continue

            # 解析配置项
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()

                # 特殊处理none值
                if value.lower() == 'none':
                    config_dict[current_door_type][key] = None
                # 处理ID列表
                elif key in ['loading_node_ids', 'weld_comp_ids', 'glue_comp_ids',
                             'INNER_COMP_IDS', 'MARKED_COMP_IDS', 'WINDOW_FRAME_COMP_ID']:
                    config_dict[current_door_type][key] = set(int(x) for x in value.split(','))
                # 处理单个ID
                elif key in ['OUTER_COMP_ID', 'REINFORCE_A_COMP_ID', 'REINFORCE_B_COMP_ID',
                                 'LOCK_REINFORCE_COMP_ID', 'UPPER_HINGE_REINFORCE_COMP_ID',
                                 'LOWER_HINGE_REINFORCE_COMP_ID']:
                    config_dict[current_door_type][key] = int(value)
                else:
                    config_dict[current_door_type][key] = value
    print('config_dict', config_dict)
    return config_dict
# 从文件名中提取车门类型
def extract_door_type(filename):
    # 支持两种文件名格式: R_024_00013_components.pkl 或 R-024-00013-components.pkl
    match = re.search(r'[R_\-](?P<door_type>\d{3})[_\-]', filename)
    if match:
        return match.group('door_type')
    return None
# ------------------------------------------------------------------------
# 1. 添加锁加强板通道
def add_hinge_reinforce_channel(npz_file, config, output_dir):

    # 加载现有数据
    data = np.load(npz_file)
    voxel = data['voxel']
    entity_comp = data['entity_comp']  # entity_comp[i, j, k] = comp_id_val 对应的是体素索引在的组件的id是多少

    # 获取原始通道数
    original_channels = voxel.shape[3]  # original_channels = 13 voxel.shape = (X, Y, Z, C)，前三维是体素空间位置，第四维是通道
    print("original_channels", original_channels)

    # 确定需要添加的新通道数
    new_channels = 2  # 锁加强板和窗框
    new_voxel = np.zeros((*voxel.shape[:3], original_channels + new_channels), dtype=np.float32) # 空数组 new_voxel.shape = (X, Y, Z, 17)
    new_voxel[:, :, :, :original_channels] = voxel # 将原始的15通道数据复制到新数组 new_voxel 的前13个通道中。

    # 通道索引
    upper_hinge_channel = original_channels  # 15
    lower_hinge_channel = original_channels + 1  # 16

    # 获取上铰链加强板组件ID
    upper_hinge_id = config.get('UPPER_HINGE_REINFORCE_COMP_ID', None)  # 后续batch中从某一车门中获取锁的id
    if upper_hinge_id is not None:
        upper_positions = np.where(entity_comp == upper_hinge_id) # 寻找到数组中锁id所包含的索引
        new_voxel[upper_positions[0], upper_positions[1], upper_positions[2], upper_hinge_channel] = 1.0  # 上铰链通道：是上铰链索引在的地方，放1
        print(f"  → 添加上铰链加强板 (ID: {upper_hinge_id}) 到通道 {upper_hinge_channel}")

    # 获取下铰链加强板组件ID
    lower_hinge_id = config.get('LOWER_HINGE_REINFORCE_COMP_ID', None)
    if lower_hinge_id is not None:
        lower_positions = np.where(entity_comp == lower_hinge_id)
        new_voxel[lower_positions[0], lower_positions[1], lower_positions[2], lower_hinge_channel] = 1.0
        print(f"  → 添加下铰链加强板 (ID: {lower_hinge_id}) 到通道 {lower_hinge_channel}")

    # 保存更新后的文件
    base_name = os.path.basename(npz_file)
    output_path = os.path.join(output_dir, base_name)
    np.savez_compressed(
        output_path,
        voxel=new_voxel,
        entity_comp=entity_comp,
        loading_point=data['loading_point'] if 'loading_point' in data else np.array([-1, -1, -1])
    )
    print(f"  ✅ 已添加上下铰链通道到 {base_name}")

    return new_voxel
# ------------------------------------------------------------------------
# 可视化所有通道（更新版）
def visualize_all_channels_updated(voxel_data, output_path=None, title=None):
    n_channels = voxel_data.shape[3]
    channel_names = [
        "外板", "内板", "中间板",
        "胶", "焊点", "RBE2",
        "外板厚度", "内板厚度", "其他板厚度",
        "内外板距离差值",
        "腰线加强板A", "腰线加强板B",
        "标记组件",
        "锁加强板",  # 通道13
        "窗框",     # 通道14
        "上铰链加强板",  # 新增通道15
        "下铰链加强板"   # 新增通道16
    ]

    # 如果通道数多于名称数，添加默认名称
    if n_channels > len(channel_names):
        for i in range(len(channel_names), n_channels):
            channel_names.append(f"通道 {i}")

    n_cols = 4
    n_rows = (n_channels + n_cols - 1) // n_cols
    fig = plt.figure(figsize=(6 * n_cols, 6 * n_rows))

    for ch in range(n_channels):
        ax = fig.add_subplot(n_rows, n_cols, ch + 1, projection='3d')
        channel_data = voxel_data[..., ch]
        x, y, z = np.where(channel_data > 0)

        if len(x) == 0:
            ax.set_title(f"{channel_names[ch]} (空)")
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.set_zlim(0, 1)
            ax.set_box_aspect([1, 1, 1])
            ax.set_xticks([])
            ax.set_yticks([])
            ax.set_zticks([])
            continue

        # 厚度和距离差值通道使用热力图
        if ch in [6, 7, 8, 9]:
            values = channel_data[x, y, z]
            sc = ax.scatter(x, y, z, c=values, cmap='hot', s=5, alpha=0.7)
            plt.colorbar(sc, ax=ax, shrink=0.6, label='值')
        # 其他通道用固定颜色
        else:
            colors = [
                'blue', 'green', 'orange', 'red', 'cyan', 'magenta',
                'yellow', 'purple', 'pink', 'brown', 'lime', 'navy',
                'gold', 'teal', 'coral', 'skyblue', 'darkgreen'  # 新增颜色
            ]
            color_idx = min(ch, len(colors) - 1)
            ax.scatter(x, y, z, c=colors[color_idx], s=5, alpha=0.7)

        min_x, max_x = x.min(), x.max()
        min_y, max_y = y.min(), y.max()
        min_z, max_z = z.min(), z.max()
        overall_min = min(min_x, min_y, min_z)
        overall_max = max(max_x, max_y, max_z)
        ax.set_xlim(overall_min, overall_max)
        ax.set_ylim(overall_min, overall_max)
        ax.set_zlim(overall_min, overall_max)
        ax.set_box_aspect([1, 1, 1])

        ax.set_title(channel_names[ch], fontsize=14)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')

    if title:
        plt.suptitle(title, fontsize=24, y=0.98)
    plt.tight_layout(pad=3.0)

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"可视化已保存至: {output_path}")

    plt.close(fig)

# ------------------------------------------------------------------------
# 后处理主函数
def post_process_npz_files(npz_directory, config_file_path, output_dir, visualize=False):
    # 加载车门配置,txt文件包括id
    door_configs = load_door_config(config_file_path)
    print(f"加载车门配置完成，共 {len(door_configs)} 种车门类型")
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 收集所有npz文件
    npz_files = [f for f in os.listdir(npz_directory) if f.endswith('_multi.npz')]
    print(f"找到 {len(npz_files)} 个NPZ文件待处理")

    # 创建可视化目录
    vis_dir = os.path.join(output_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)

    processed_count = 0
    for npz_file in npz_files:
        file_path = os.path.join(npz_directory, npz_file)
        print(f"\n▶ 处理文件 {processed_count+1}/{len(npz_files)}: {npz_file}")

        # 提取车门类型
        door_type = extract_door_type(npz_file)
        if not door_type:
            print(f"  ⚠ 警告: 无法从文件名中提取车门类型，跳过")
            continue

        # 获取配置
        if door_type not in door_configs:
            print(f"  ⚠ 警告: 找不到车门类型 {door_type} 的配置，跳过")
            continue

        config = door_configs[door_type]
        print(f"  → 使用车门类型 {door_type} 的配置")
        print(f"  → 上铰链ID: {config.get('UPPER_HINGE_REINFORCE_COMP_ID', '无')}")
        print(f"  → 下铰链ID: {config.get('LOWER_HINGE_REINFORCE_COMP_ID', '无')}")

        # 添加上下加强板通道
        add_hinge_reinforce_channel(file_path, config, output_dir)
        processed_count += 1

        # 可视化（可选）
        if visualize:
            # 加载更新后的文件
            updated_file = os.path.join(output_dir, npz_file)
            data = np.load(updated_file)
            voxel = data['voxel']

            # 可视化所有通道
            vis_path = os.path.join(vis_dir, f"{os.path.splitext(npz_file)[0]}_updated.png")
            visualize_all_channels_updated(voxel, output_path=vis_path,
                                           title=f"Updated Voxel - {npz_file}")

        print(f"\n处理完成! 成功处理 {processed_count}/{len(npz_files)} 个文件")

# ------------------------------------------------------------------------
# 主程序
if __name__ == "__main__":
    # 配置路径
    npz_directory = r"D:\Aprojecdtdoor\Dataset_Construction\data_npy2_sw\F-006"  # 已生成的npz文件目录
    config_file_path = r"D:\Aprojecdtdoor\Dataset_Construction\code-v2\data_process\door_config(sw_jl).txt"  # 配置文件路径
    output_dir = r"D:\Aprojecdtdoor\Dataset_Construction\data_huidutu_sw_jl\F_006"  # 输出目录

    # 执行后处理
    post_process_npz_files(npz_directory, config_file_path, output_dir, visualize=True)
    print("\n所有文件处理完成！")


