#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的车门刚度预测训练脚本
================================

主要改进：
1. 改进的数据预处理和归一化
2. 学习率调度优化
3. 早停机制
4. 模型集成
5. 更好的损失函数组合

作者：AI Assistant
创建时间：2025-01-25
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau
import numpy as np
import matplotlib.pyplot as plt
import time
import os
from dataset_tongdao_auto import CarDoorStiffnessDataset

# 导入模型
from model import ResNet_18_2D
from model34_new import ResNet_34_2D
from model50_new import ResNet_50_2D

# 配置参数 - 根据您的需求调整
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
TRAIN_PATH = r"D:\Aprojecdtdoor\Dataset_Construction\code-v2\sample\CX\CX_lower_upper_wf\train"
BATCH_SIZE = 16  # 批大小
NUM_EPOCHS = 500  # 增加训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-5  # 权重衰减
SMAPE_EPS = 1e-6  # SMAPE计算中的小量
MODEL_SAVE_INTERVAL = 50  # 模型保存间隔
ATTENTION_FACTOR = 0.5  # 注意力因子

# 早停参数
EARLY_STOP_PATIENCE = 50
EARLY_STOP_MIN_DELTA = 1e-4

def smape_scalar(pred, true, eps=SMAPE_EPS):
    """计算SMAPE指标"""
    pred = pred.view(-1)
    true = true.view(-1)
    return 1 - torch.abs(pred - true) / (torch.abs(pred) + torch.abs(true) + eps)

def improved_loss_function(pred, true, alpha=0.5, beta=0.3, gamma=0.2):
    """改进的损失函数组合"""
    mse_loss = nn.MSELoss()(pred, true)
    mae_loss = nn.L1Loss()(pred, true)
    
    # SMAPE损失
    smape_loss = 1 - torch.mean(smape_scalar(pred, true))
    
    # 组合损失
    total_loss = alpha * mse_loss + beta * mae_loss + gamma * smape_loss
    return total_loss, mse_loss, mae_loss, smape_loss

class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=50, min_delta=1e-4):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')
        
    def __call__(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            return False
        else:
            self.counter += 1
            return self.counter >= self.patience

def train_improved():
    """改进的训练函数"""
    print("=== 改进的车门刚度预测训练 ===")
    
    # 1. 准备数据集和数据加载器
    train_dataset = CarDoorStiffnessDataset(
        root_dir=TRAIN_PATH,
        target_size=(270, 270),  # 设置统一尺寸
        padding_mode='constant'
    )
    
    train_dl = DataLoader(
        train_dataset,
        batch_size=BATCH_SIZE,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    print(f"训练集大小: {len(train_dataset)} 个样本")
    print(f"批次数量: {len(train_dl)}")
    print(f"通道数量: {len(train_dataset.channel_dirs)}")
    
    # 2. 初始化模型 - 根据通道数选择合适的模型
    num_channels = len(train_dataset.channel_dirs)
    print(f"检测到 {num_channels} 个通道")
    
    model = ResNet_34_2D(  # 使用ResNet-34作为基础模型
        in_channels=num_channels,
        attention_factor=ATTENTION_FACTOR
    ).to(DEVICE)
    
    print(f"模型已加载到设备: {DEVICE}")
    
    # 3. 设置优化器和学习率调度
    optimizer = torch.optim.AdamW(  # 使用AdamW优化器
        model.parameters(),
        lr=LEARNING_RATE,
        weight_decay=WEIGHT_DECAY,
        betas=(0.9, 0.999)
    )
    
    # 组合学习率调度器
    scheduler_cosine = CosineAnnealingLR(optimizer, T_max=NUM_EPOCHS)
    scheduler_plateau = ReduceLROnPlateau(optimizer, mode='min', factor=0.5, 
                                        patience=20, verbose=True)
    
    # 4. 早停机制
    early_stopping = EarlyStopping(patience=EARLY_STOP_PATIENCE, 
                                 min_delta=EARLY_STOP_MIN_DELTA)
    
    # 5. 创建保存目录
    os.makedirs("checkpoints", exist_ok=True)
    
    # 6. 训练记录
    train_losses = []
    train_smapes = []
    best_loss = float('inf')
    
    print(f"开始训练... 设备: {DEVICE}")
    start_time = time.time()
    
    # 开始训练
    for epoch in range(1, NUM_EPOCHS + 1):
        model.train()
        epoch_total_loss = 0.0
        epoch_mse_loss = 0.0
        epoch_mae_loss = 0.0
        epoch_smape_loss = 0.0
        epoch_smape = 0.0
        
        for batch_idx, (data, target, img_files, model_ids, run_ids) in enumerate(train_dl):
            data, target = data.to(DEVICE), target.to(DEVICE)
            
            optimizer.zero_grad()
            output = model(data)
            
            # 计算改进的损失函数
            total_loss, mse_loss, mae_loss, smape_loss = improved_loss_function(output, target)
            
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 计算SMAPE指标
            smape_batch = torch.mean(smape_scalar(output, target))
            
            # 累计损失
            epoch_total_loss += total_loss.item()
            epoch_mse_loss += mse_loss.item()
            epoch_mae_loss += mae_loss.item()
            epoch_smape_loss += smape_loss.item()
            epoch_smape += smape_batch.item()
        
        # 计算平均损失
        avg_total_loss = epoch_total_loss / len(train_dl)
        avg_mse_loss = epoch_mse_loss / len(train_dl)
        avg_mae_loss = epoch_mae_loss / len(train_dl)
        avg_smape_loss = epoch_smape_loss / len(train_dl)
        avg_smape = epoch_smape / len(train_dl)
        
        # 记录训练历史
        train_losses.append(avg_total_loss)
        train_smapes.append(avg_smape)
        
        # 更新学习率
        scheduler_cosine.step()
        scheduler_plateau.step(avg_total_loss)
        
        # 打印进度
        if epoch % 10 == 0 or epoch == 1:
            print(f"Epoch {epoch:03d}/{NUM_EPOCHS} | "
                  f"Total Loss: {avg_total_loss:.4e} | "
                  f"MSE: {avg_mse_loss:.4e} | "
                  f"MAE: {avg_mae_loss:.4e} | "
                  f"SMAPE: {avg_smape:.4f} | "
                  f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        # 保存最佳模型
        if avg_total_loss < best_loss:
            best_loss = avg_total_loss
            torch.save(model.state_dict(), "checkpoints/best_model.pth")
            print(f"✅ 保存最佳模型 (Epoch {epoch}, Loss: {best_loss:.4e})")
        
        # 定期保存模型
        if epoch % MODEL_SAVE_INTERVAL == 0:
            save_path = f"checkpoints/model_epoch{epoch}.pth"
            torch.save(model.state_dict(), save_path)
            print(f"📁 模型保存至: {save_path}")
        
        # 早停检查
        if early_stopping(avg_total_loss):
            print(f"🛑 早停触发 (Epoch {epoch})")
            break
    
    # 训练完成
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n🎉 训练完成!")
    print(f"⏱️ 总耗时: {total_time:.2f} 秒 ({total_time/60:.2f} 分钟)")
    print(f"🏆 最佳损失: {best_loss:.4e}")
    
    # 保存最终模型
    torch.save(model.state_dict(), "model_final.pth")
    print("💾 最终模型已保存为 model_final.pth")
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses)
    plt.title('Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(train_smapes)
    plt.title('Training SMAPE')
    plt.xlabel('Epoch')
    plt.ylabel('SMAPE')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('training_metrics_improved.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📊 训练曲线已保存为 training_metrics_improved.png")

if __name__ == "__main__":
    train_improved()
