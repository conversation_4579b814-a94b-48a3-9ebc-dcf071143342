import os
import torch
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，避免后端报错
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import matplotlib.font_manager as fm

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimSun']  # 使用宋体替代黑体
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
# 导入新的数据集类和模型
from dataset_tongdao import CarDoorStiffnessDataset  # 替换为您的双通道灰度图数据集类
from model import ResNet_18_2D  # 替换为您的2D ResNet模型
# from model34_new import ResNet_34_2D # ResNet_34模型
# from model50_new import ResNet_50_2D # ResNet_50模型
# 配置参数 - 根据您的需求调整
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
VAL_PATH = "/home/<USER>/1yaojy/pipei/val"
BATCH_SIZE = 16  # 批大小
NUM_EPOCHS = 300  # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-5  # 权重衰减
SMAPE_EPS = 1e-6  # SMAPE计算中的小量
MODEL_SAVE_INTERVAL = 50  # 模型保存间隔
ATTENTION_FACTOR = 0.5  # 注意力因子


def smape(pred, true, eps=SMAPE_EPS):
    """计算对称平均绝对百分比误差"""
    pred = pred.view(-1)
    true = true.view(-1)
    return 1 - torch.abs(pred - true) / (torch.abs(pred) + torch.abs(true) + eps)

def stiffness_mse(pred, true):
    return torch.mean((pred - true) ** 2)

def validate():
    # 1. 初始化模型
    model = ResNet_18_2D(
        in_channels=3,  # 双通道输入
        attention_factor=ATTENTION_FACTOR
    ).to(DEVICE)      # .to(DEVICE)：模型放到显卡上训练
    # 2. 加载训练好的模型权重
    model.load_state_dict(torch.load("model_final.pth", map_location=DEVICE))
    model.eval()# 设置为评估模式
    print("模型加载成功，已设置为评估模式")
    # 3. 准备验证数据集
    val_dataset = CarDoorStiffnessDataset(
        root_dir=VAL_PATH,
        minite_dir="minite_points",
        thickness_dir="other_thickness",
        combination_dir="thickness_combination",
        data_file="extracted_data.npy"
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=1,  # 每个样本单独处理
        shuffle=False,  # 不随机打乱，保持原始顺序
        num_workers=4,  # 根据CPU核心数调整
        pin_memory=True  # 加速GPU数据传输
    )
    print(f"验证集大小: {len(val_dataset)} 样本")
    # 4. 收集预测结果
    preds = []
    trues = []
    run_ids = []
    smape_values = []
    with torch.no_grad(): # 禁用梯度计算，节省内存
        for images, stiffness, run_id in val_loader:
            images = images.to(DEVICE)
            stiffness = stiffness.to(DEVICE)

            # 模型预测
            pred_stiffness = model(images)
            # 计算SMAPE
            smape_val = smape(pred_stiffness, stiffness).item()
            # 收集结果
            preds.append(pred_stiffness.item())
            trues.append(stiffness.item())
            run_ids.append(run_id[0])  # 因为run_id是元组，取第一个元素
            smape_values.append(smape_val)
            # 打印单个样本结果
            print(f"Run ID: {run_id[0]} | 真实刚度: {stiffness.item():.4f} | "
                  f"预测刚度: {pred_stiffness.item():.4f} | SMAPE: {smape_val:.4f}")

    # 转换为numpy数组
    preds = np.array(preds)
    trues = np.array(trues)
    smape_values = np.array(smape_values)
    if len(trues) == 0:
        print("❌ 未收集到验证数据。请检查VAL_PATH和数据集格式。")
        return

    # 5. 计算整体评估指标
    mse = np.mean((preds - trues) ** 2)
    mae = np.mean(np.abs(preds - trues)) # 新增
    smape_val = 1 - np.mean(np.abs(preds - trues) / (np.abs(preds) + np.abs(trues) + SMAPE_EPS)) # 对称平均百分比误差
    avg_smape = np.mean(smape_values)
    r_squared = 1 - np.sum((trues - preds) ** 2) / np.sum((trues - np.mean(trues)) ** 2)
    print("\n===== 验证结果 =====")
    print(f"MSE: {mse:.6f}")
    print(f"MAE: {mae:.6f}")
    print(f"平均SMAPE: {avg_smape:.6f}")
    print(f"R²: {r_squared:.6f}")
    print(f"Validation SMAPE: {smape_val:.6f}")

    # # 6. 绘制预测值与真实值的散点图
    plt.figure(figsize=(6, 6))
    plt.scatter(trues, preds, alpha=0.6)
    plt.plot([trues.min(), trues.max()], [trues.min(), trues.max()], 'r--', lw=2)
    plt.xlabel("True Stiffness")
    plt.ylabel("Predicted Stiffness")
    plt.title("Validation: True vs Predicted Stiffness")
    plt.grid(True)
    plt.savefig("validation_scatter.png", dpi=200)
    print("📈 Scatter plot saved as validation_scatter.png")

if __name__ == "__main__":
    validate()
