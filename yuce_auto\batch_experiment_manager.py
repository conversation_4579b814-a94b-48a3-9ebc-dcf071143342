#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量实验管理器 - Batch Experiment Manager
==========================================

功能概述：
1. GUI界面管理9组实验配置
2. 动态生成训练和预测脚本
3. 批量执行实验流程
4. 结果收集和分析
5. 错误处理和进度显示

作者：AI Assistant
创建时间：2025-01-18
"""

import os
import re
import shutil
import subprocess
import threading
import time
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import json

class BatchExperimentManager:
    def __init__(self):
        """初始化批量实验管理器"""
        self.root = tk.Tk()
        self.root.title("批量实验管理器 - Batch Experiment Manager")
        self.root.geometry("1200x800")
        
        # 实验配置存储
        self.experiments = {}
        self.current_experiment = 0
        self.is_running = False
        self.process = None

        # 数据路径配置
        self.train_data_path = r"D:\Aprojecdtdoor\Dataset_Construction\code-v2\sample\YX_N\40\train"
        self.val_data_path = r"D:\Aprojecdtdoor\Dataset_Construction\code-v2\sample\YX_N\40\val"

        # 先初始化默认实验配置（GUI需要用到）
        self.init_default_experiments()

        # 然后设置GUI界面
        self.setup_ui()

        # 最后创建必要的目录
        self.setup_directories()
        
    def setup_directories(self):
        """创建必要的目录结构"""
        directories = [
            "train_results",
            "prediction_results",
            "experiment_reports"
        ]

        for dir_name in directories:
            os.makedirs(dir_name, exist_ok=True)

        # 清理可能存在的共享checkpoints文件夹冲突
        self.cleanup_shared_checkpoints()

    def cleanup_shared_checkpoints(self):
        """清理共享的checkpoints文件夹，避免命名冲突"""
        shared_checkpoints = "checkpoints"
        if os.path.exists(shared_checkpoints):
            try:
                # 备份现有文件到临时位置
                backup_dir = "checkpoints_backup"
                if os.path.exists(backup_dir):
                    import shutil
                    shutil.rmtree(backup_dir)

                os.rename(shared_checkpoints, backup_dir)
                # 安全地记录日志
                self.safe_log(f"⚠️ 已将共享checkpoints文件夹备份到: {backup_dir}")
                self.safe_log("✅ 现在每个实验将使用独立的checkpoints文件夹")
            except Exception as e:
                self.safe_log(f"⚠️ 清理共享checkpoints时出错: {str(e)}")

    def safe_log(self, message):
        """安全的日志记录方法"""
        if hasattr(self, 'log_text') and self.log_text is not None:
            # 如果GUI已经初始化，使用GUI日志
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        else:
            # 如果GUI未初始化，使用print
            print(message)

    def init_default_experiments(self):
        """初始化默认实验配置"""
        default_configs = [
            {
                "name": "实验1",
                "model": "ResNet_18_2D",
                "batch_size": 16,
                "num_epochs": 10,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "快速测试实验 (10 epochs)"
            },
            {
                "name": "实验2",
                "model": "ResNet_34_2D",
                "batch_size": 16,
                "num_epochs": 20,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "短期验证实验 (20 epochs)"
            },
            {
                "name": "实验3",
                "model": "ResNet_50_2D",
                "batch_size": 16,
                "num_epochs": 20,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "中期训练实验 (50 epochs)"
            },
            {
                "name": "实验4",
                "model": "ResNet_18_2D",
                "batch_size": 16,
                "num_epochs": 10,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "标准训练实验 (100 epochs)"
            },
            {
                "name": "实验5",
                "model": "ResNet_34_2D",
                "batch_size": 16,
                "num_epochs": 10,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "长期训练实验 (200 epochs)"
            },
            {
                "name": "实验6",
                "model": "ResNet_18_2D",
                "batch_size": 16,
                "num_epochs": 5,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "超快速测试 (5 epochs)"
            },
            {
                "name": "实验7",
                "model": "ResNet_50_2D",
                "batch_size": 16,
                "num_epochs": 30,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "深度网络长训练 (300 epochs)"
            },
            {
                "name": "实验8",
                "model": "ResNet_34_2D",
                "batch_size": 16,
                "num_epochs": 20,
                "learning_rate": 0.001,
                "weight_decay": 1e-6,
                "attention_factor": 0.5,
                "description": "超长训练实验 (500 epochs)"
            },
            {
                "name": "实验9",
                "model": "ResNet_18_2D",
                "batch_size": 16,
                "num_epochs": 20,
                "learning_rate": 0.001,
                "weight_decay": 1e-5,
                "attention_factor": 0.5,
                "description": "极长训练实验 (1000 epochs)"
            }
        ]
        
        for i, config in enumerate(default_configs):
            self.experiments[f"exp_{i+1}"] = config
            
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="批量实验管理器",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=10)

        # 数据路径配置区域
        self.setup_data_path_config(main_frame)
        
        # 实验配置区域
        self.setup_experiment_config_area(main_frame)

        # 控制按钮区域
        self.setup_control_buttons(main_frame)

        # 进度显示区域
        self.setup_progress_area(main_frame)

    def setup_data_path_config(self, parent):
        """设置数据路径配置区域"""
        path_frame = ttk.LabelFrame(parent, text="数据路径配置", padding="10")
        path_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        # 训练数据路径
        ttk.Label(path_frame, text="训练数据路径:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.train_path_var = tk.StringVar(value=self.train_data_path)
        train_path_entry = ttk.Entry(path_frame, textvariable=self.train_path_var, width=60)
        train_path_entry.grid(row=0, column=1, padx=5, sticky=(tk.W, tk.E))
        ttk.Button(path_frame, text="浏览",
                  command=lambda: self.browse_folder(self.train_path_var)).grid(row=0, column=2, padx=5)

        # 验证数据路径
        ttk.Label(path_frame, text="验证数据路径:").grid(row=1, column=0, sticky=tk.W, padx=5)
        self.val_path_var = tk.StringVar(value=self.val_data_path)
        val_path_entry = ttk.Entry(path_frame, textvariable=self.val_path_var, width=60)
        val_path_entry.grid(row=1, column=1, padx=5, sticky=(tk.W, tk.E))
        ttk.Button(path_frame, text="浏览",
                  command=lambda: self.browse_folder(self.val_path_var)).grid(row=1, column=2, padx=5)

        path_frame.columnconfigure(1, weight=1)

    def browse_folder(self, path_var):
        """浏览文件夹"""
        from tkinter import filedialog
        folder = filedialog.askdirectory(initialdir=path_var.get())
        if folder:
            path_var.set(folder)
        
    def setup_experiment_config_area(self, parent):
        """设置实验配置区域"""
        # 配置框架
        config_frame = ttk.LabelFrame(parent, text="实验配置", padding="10")
        config_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # 创建表格头
        headers = ["实验", "模型", "批大小", "训练轮数", "学习率", "权重衰减", "注意力因子", "描述"]
        for i, header in enumerate(headers):
            ttk.Label(config_frame, text=header, font=("Arial", 10, "bold")).grid(
                row=0, column=i, padx=5, pady=5, sticky=tk.W)
        
        # 存储输入控件的引用
        self.config_widgets = {}
        
        # 创建9行实验配置
        for exp_idx in range(9):
            exp_key = f"exp_{exp_idx + 1}"
            self.config_widgets[exp_key] = {}
            
            row = exp_idx + 1
            
            # 实验名称
            name_label = ttk.Label(config_frame, text=f"实验{exp_idx + 1}")
            name_label.grid(row=row, column=0, padx=5, pady=2)
            
            # 模型选择下拉框
            default_model = self.experiments.get(exp_key, {}).get("model", "ResNet_18_2D")
            model_var = tk.StringVar(value=default_model)
            model_combo = ttk.Combobox(config_frame, textvariable=model_var, 
                                     values=["ResNet_18_2D", "ResNet_34_2D", "ResNet_50_2D"],
                                     width=12, state="readonly")
            model_combo.grid(row=row, column=1, padx=5, pady=2)
            self.config_widgets[exp_key]["model"] = model_var
            
            # 批大小
            default_batch = self.experiments.get(exp_key, {}).get("batch_size", 16)
            batch_var = tk.StringVar(value=str(default_batch))
            batch_entry = ttk.Entry(config_frame, textvariable=batch_var, width=8)
            batch_entry.grid(row=row, column=2, padx=5, pady=2)
            self.config_widgets[exp_key]["batch_size"] = batch_var

            # 训练轮数 - 改为下拉框，方便选择
            default_epochs = self.experiments.get(exp_key, {}).get("num_epochs", 100)
            epochs_var = tk.StringVar(value=str(default_epochs))
            epoch_values = ["5", "10", "20", "50", "100", "200", "300", "500", "800", "1000"]
            epochs_combo = ttk.Combobox(config_frame, textvariable=epochs_var,
                                       values=epoch_values, width=8)
            epochs_combo.grid(row=row, column=3, padx=5, pady=2)
            self.config_widgets[exp_key]["num_epochs"] = epochs_var

            # 学习率
            default_lr = self.experiments.get(exp_key, {}).get("learning_rate", 0.001)
            lr_var = tk.StringVar(value=str(default_lr))
            lr_entry = ttk.Entry(config_frame, textvariable=lr_var, width=10)
            lr_entry.grid(row=row, column=4, padx=5, pady=2)
            self.config_widgets[exp_key]["learning_rate"] = lr_var
            
            # 权重衰减
            default_wd = self.experiments.get(exp_key, {}).get("weight_decay", 1e-5)
            wd_var = tk.StringVar(value=str(default_wd))
            wd_entry = ttk.Entry(config_frame, textvariable=wd_var, width=10)
            wd_entry.grid(row=row, column=5, padx=5, pady=2)
            self.config_widgets[exp_key]["weight_decay"] = wd_var

            # 注意力因子
            default_af = self.experiments.get(exp_key, {}).get("attention_factor", 0.5)
            af_var = tk.StringVar(value=str(default_af))
            af_entry = ttk.Entry(config_frame, textvariable=af_var, width=8)
            af_entry.grid(row=row, column=6, padx=5, pady=2)
            self.config_widgets[exp_key]["attention_factor"] = af_var
            
            # 描述
            default_desc = self.experiments.get(exp_key, {}).get("description", f"实验{exp_idx + 1}")
            desc_var = tk.StringVar(value=default_desc)
            desc_entry = ttk.Entry(config_frame, textvariable=desc_var, width=20)
            desc_entry.grid(row=row, column=7, padx=5, pady=2)
            self.config_widgets[exp_key]["description"] = desc_var

    def setup_control_buttons(self, parent):
        """设置控制按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=3, column=0, columnspan=3, pady=10)

        # 配置管理按钮
        ttk.Button(button_frame, text="💾 保存配置",
                  command=self.save_config).grid(row=0, column=0, padx=5)
        ttk.Button(button_frame, text="📁 加载配置",
                  command=self.load_config).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="🔄 重置默认",
                  command=self.reset_to_default).grid(row=0, column=2, padx=5)

        # 实验控制按钮
        self.start_button = ttk.Button(button_frame, text="🚀 开始批量实验",
                                      command=self.start_experiments)
        self.start_button.grid(row=0, column=3, padx=10)

        self.pause_button = ttk.Button(button_frame, text="⏸ 暂停",
                                      command=self.pause_experiments, state="disabled")
        self.pause_button.grid(row=0, column=4, padx=5)

        self.stop_button = ttk.Button(button_frame, text="🛑 停止",
                                     command=self.stop_experiments, state="disabled")
        self.stop_button.grid(row=0, column=5, padx=5)

        # 结果查看按钮
        ttk.Button(button_frame, text="📊 查看结果",
                  command=self.view_results).grid(row=0, column=6, padx=10)
        ttk.Button(button_frame, text="📈 生成报告",
                  command=self.generate_report).grid(row=0, column=7, padx=5)

    def setup_progress_area(self, parent):
        """设置进度显示区域"""
        progress_frame = ttk.LabelFrame(parent, text="实验进度", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        # 当前实验信息
        self.current_exp_label = ttk.Label(progress_frame, text="准备就绪",
                                          font=("Arial", 12))
        self.current_exp_label.grid(row=0, column=0, sticky=tk.W, pady=5)

        # 总体进度条
        self.overall_progress = ttk.Progressbar(progress_frame, length=400, mode='determinate')
        self.overall_progress.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)

        # 当前实验进度条
        self.current_progress = ttk.Progressbar(progress_frame, length=400, mode='determinate')
        self.current_progress.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=5)

        # 日志显示区域
        log_frame = ttk.Frame(progress_frame)
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)

        self.log_text = tk.Text(log_frame, height=10, width=80)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        progress_frame.columnconfigure(0, weight=1)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update()

    def update_experiments_from_ui(self):
        """从UI更新实验配置"""
        for exp_key in self.experiments.keys():
            try:
                self.experiments[exp_key].update({
                    "model": self.config_widgets[exp_key]["model"].get(),
                    "batch_size": int(self.config_widgets[exp_key]["batch_size"].get()),
                    "num_epochs": int(self.config_widgets[exp_key]["num_epochs"].get()),
                    "learning_rate": float(self.config_widgets[exp_key]["learning_rate"].get()),
                    "weight_decay": float(self.config_widgets[exp_key]["weight_decay"].get()),
                    "attention_factor": float(self.config_widgets[exp_key]["attention_factor"].get()),
                    "description": self.config_widgets[exp_key]["description"].get()
                })
            except ValueError as e:
                messagebox.showerror("配置错误", f"实验 {exp_key} 参数格式错误: {str(e)}")
                return False
        return True

    def validate_experiments(self):
        """验证实验配置"""
        for exp_key, config in self.experiments.items():
            # 检查参数范围
            if not (8 <= config["batch_size"] <= 32):
                messagebox.showerror("参数错误", f"{exp_key}: 批大小应在8-32范围内")
                return False
            if not (1 <= config["num_epochs"] <= 1000):
                messagebox.showerror("参数错误", f"{exp_key}: 训练轮数应在1-1000范围内")
                return False
            if not (0.0001 <= config["learning_rate"] <= 0.01):
                messagebox.showerror("参数错误", f"{exp_key}: 学习率应在0.0001-0.01范围内")
                return False
            if not (1e-6 <= config["weight_decay"] <= 1e-3):
                messagebox.showerror("参数错误", f"{exp_key}: 权重衰减应在1e-6到1e-3范围内")
                return False
            if not (0.1 <= config["attention_factor"] <= 1.0):
                messagebox.showerror("参数错误", f"{exp_key}: 注意力因子应在0.1-1.0范围内")
                return False
        return True

    def generate_experiment_name(self, config):
        """生成实验文件夹名称"""
        model_short = {
            "ResNet_18_2D": "R18",
            "ResNet_34_2D": "R34",
            "ResNet_50_2D": "R50"
        }[config["model"]]

        lr_str = str(config["learning_rate"]).replace(".", "").replace("0", "")
        if not lr_str:
            lr_str = "0001"
        wd_str = f"{config['weight_decay']:.0e}".replace("-", "").replace("+", "")
        af_str = str(int(config["attention_factor"] * 100)).zfill(2)

        return f"{model_short}_BS{config['batch_size']}_EP{config['num_epochs']}_LR{lr_str}_WD{wd_str}_AF{af_str}"

    def save_experiment_config(self, config, output_dir):
        """保存实验配置到文件"""
        config_path = os.path.join(output_dir, "config.txt")

        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(f"实验配置\n")
            f.write(f"=" * 50 + "\n")
            f.write(f"实验名称: {config['name']}\n")
            f.write(f"模型: {config['model']}\n")
            f.write(f"批大小: {config['batch_size']}\n")
            f.write(f"训练轮数: {config['num_epochs']}\n")
            f.write(f"学习率: {config['learning_rate']}\n")
            f.write(f"权重衰减: {config['weight_decay']}\n")
            f.write(f"注意力因子: {config['attention_factor']}\n")
            f.write(f"描述: {config['description']}\n")
            f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

    def replace_model_imports(self, content, model_choice):
        """基于代码内容替换模型导入"""

        # 定义模型配置映射
        model_configs = {
            "ResNet_18_2D": {
                "active_import": "from model import ResNet_18_2D  # 替换为您的2D ResNet模型",
                "comment_imports": [
                    "#from model34_new import ResNet_34_2D # ResNet_34模型",
                    "# from model50_new import ResNet_50_2D # ResNet_50模型"
                ]
            },
            "ResNet_34_2D": {
                "active_import": "from model34_new import ResNet_34_2D # ResNet_34模型",
                "comment_imports": [
                    "#from model import ResNet_18_2D  # 替换为您的2D ResNet模型",
                    "# from model50_new import ResNet_50_2D # ResNet_50模型"
                ]
            },
            "ResNet_50_2D": {
                "active_import": "from model50_new import ResNet_50_2D # ResNet_50模型",
                "comment_imports": [
                    "#from model import ResNet_18_2D  # 替换为您的2D ResNet模型",
                    "#from model34_new import ResNet_34_2D # ResNet_34模型"
                ]
            }
        }

        config = model_configs[model_choice]

        # 替换模型导入行
        patterns = [
            r'#?from model import ResNet_18_2D.*',
            r'#?from model34_new import ResNet_34_2D.*',
            r'#?from model50_new import ResNet_50_2D.*'
        ]

        # 先注释掉所有导入
        for pattern in patterns:
            content = re.sub(pattern, lambda m: "#" + m.group(0) if not m.group(0).startswith("#") else m.group(0), content)

        # 激活选中的导入
        if model_choice == "ResNet_18_2D":
            content = re.sub(r'#from model import ResNet_18_2D.*',
                           'from model import ResNet_18_2D  # 替换为您的2D ResNet模型', content)
        elif model_choice == "ResNet_34_2D":
            content = re.sub(r'#from model34_new import ResNet_34_2D.*',
                           'from model34_new import ResNet_34_2D # ResNet_34模型', content)
        elif model_choice == "ResNet_50_2D":
            content = re.sub(r'#from model50_new import ResNet_50_2D.*',
                           'from model50_new import ResNet_50_2D # ResNet_50模型', content)

        return content

    def replace_model_instantiation(self, content, model_choice, attention_factor):
        """替换模型实例化代码"""

        # 使用字符串替换避免正则表达式问题
        content = content.replace('model = ResNet_18_2D', f'model = {model_choice}')
        content = content.replace('model = ResNet_34_2D', f'model = {model_choice}')
        content = content.replace('model = ResNet_50_2D', f'model = {model_choice}')

        # 替换注意力因子 - 使用行扫描
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'attention_factor=' in line:
                # 找到attention_factor=的位置并替换值
                parts = line.split('attention_factor=')
                if len(parts) == 2:
                    # 保留前面的部分，替换后面的值
                    before = parts[0]
                    after_parts = parts[1].split(',', 1)  # 分割第一个逗号
                    if len(after_parts) == 2:
                        lines[i] = f'{before}attention_factor={attention_factor},{after_parts[1]}'
                    else:
                        lines[i] = f'{before}attention_factor={attention_factor}'

        content = '\n'.join(lines)
        return content

    def replace_parameters(self, content, config):
        """替换训练参数"""

        # 使用行扫描替换，避免正则表达式问题
        lines = content.split('\n')
        for i, line in enumerate(lines):
            stripped_line = line.strip()
            if stripped_line.startswith('BATCH_SIZE = '):
                lines[i] = f'BATCH_SIZE = {config["batch_size"]}  # 批大小'
            elif stripped_line.startswith('NUM_EPOCHS = '):
                lines[i] = f'NUM_EPOCHS = {config["num_epochs"]}  # 训练轮数'
            elif stripped_line.startswith('LEARNING_RATE = '):
                lines[i] = f'LEARNING_RATE = {config["learning_rate"]}  # 学习率'
            elif stripped_line.startswith('WEIGHT_DECAY = '):
                lines[i] = f'WEIGHT_DECAY = {config["weight_decay"]}  # 权重衰减'
            elif stripped_line.startswith('ATTENTION_FACTOR = '):
                lines[i] = f'ATTENTION_FACTOR = {config["attention_factor"]}  # 注意力因子'

        content = '\n'.join(lines)
        return content

    def replace_data_paths(self, content, script_type="train"):
        """替换数据路径"""
        if script_type == "train":
            # 简单的字符串替换，避免正则表达式问题
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.strip().startswith('TRAIN_PATH = '):
                    lines[i] = f'TRAIN_PATH = r"{self.train_path_var.get()}"'
            content = '\n'.join(lines)
        elif script_type == "predict":
            # 简单的字符串替换，避免正则表达式问题
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.strip().startswith('VAL_PATH = '):
                    lines[i] = f'VAL_PATH = r"{self.val_path_var.get()}"'
            content = '\n'.join(lines)

        return content

    def modify_output_paths(self, content, output_dir, script_type="train"):
        """修改输出路径到指定目录"""
        # 使用字符串替换而不是正则表达式，避免转义字符问题
        if script_type == "train":
            # 修改checkpoints目录创建
            content = content.replace('os.makedirs("checkpoints", exist_ok=True)',
                                    f'os.makedirs("{output_dir}/checkpoints", exist_ok=True)')

            # 修改所有checkpoints路径（处理各种引号格式）
            content = content.replace('checkpoints/', f'{output_dir}/checkpoints/')
            content = content.replace('"checkpoints/', f'"{output_dir}/checkpoints/')
            content = content.replace("'checkpoints/", f"'{output_dir}/checkpoints/")
            content = content.replace('f"checkpoints/', f'f"{output_dir}/checkpoints/')
            content = content.replace("f'checkpoints/", f"f'{output_dir}/checkpoints/")

            # 处理特殊的checkpoints引用
            content = content.replace('"checkpoints/best_model.pth"', f'"{output_dir}/checkpoints/best_model.pth"')
            content = content.replace("'checkpoints/best_model.pth'", f"'{output_dir}/checkpoints/best_model.pth'")

            # 修改模型保存路径
            content = content.replace('"model_final.pth"', f'"{output_dir}/model_final.pth"')
            content = content.replace("'model_final.pth'", f"'{output_dir}/model_final.pth'")

            # 修改图表保存路径
            content = content.replace('"training_metrics.png"', f'"{output_dir}/training_metrics.png"')
            content = content.replace("'training_metrics.png'", f"'{output_dir}/training_metrics.png'")

        elif script_type == "predict":
            # 修改预测结果保存路径
            content = content.replace('"validation_results.csv"', f'"{output_dir}/validation_results.csv"')
            content = content.replace('"validation_scatter.png"', f'"{output_dir}/validation_scatter.png"')
            # 修改模型加载路径
            train_dir = output_dir.replace("prediction_results", "train_results")
            content = content.replace('"model_final.pth"', f'"{train_dir}/model_final.pth"')

        return content

    def verify_path_replacement(self, content, output_dir, script_type="train"):
        """验证路径替换是否成功"""
        issues = []

        if script_type == "train":
            # 检查是否还有未替换的路径
            if 'os.makedirs("checkpoints"' in content:
                issues.append("checkpoints目录创建路径未替换")
            if '"checkpoints/' in content and f'"{output_dir}/checkpoints/' not in content:
                issues.append("checkpoints路径未完全替换")
            if '"model_final.pth"' in content and f'"{output_dir}/model_final.pth"' not in content:
                issues.append("model_final.pth路径未替换")

        if issues:
            self.log_message(f"⚠️ 路径替换警告: {', '.join(issues)}")
        else:
            self.log_message(f"✅ 路径替换验证通过")

    def generate_train_script(self, config, output_dir):
        """生成训练脚本"""
        # 读取原始训练脚本 - 使用绝对路径
        train_script_path = os.path.join(os.path.dirname(__file__), "train.py")
        with open(train_script_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换模型导入
        content = self.replace_model_imports(content, config["model"])

        # 替换模型实例化
        content = self.replace_model_instantiation(content, config["model"], config["attention_factor"])

        # 替换参数
        content = self.replace_parameters(content, config)

        # 替换数据路径
        content = self.replace_data_paths(content, "train")

        # 修改输出路径
        content = self.modify_output_paths(content, output_dir, "train")

        # 验证路径替换
        self.verify_path_replacement(content, output_dir, "train")

        # 保存生成的脚本到yuce_auto目录，而不是子文件夹
        script_path = f"train_generated_{config['name'].replace(' ', '_')}.py"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return script_path

    def generate_predict_script(self, config, train_output_dir, predict_output_dir):
        """生成预测脚本"""
        # 读取原始预测脚本 - 使用绝对路径
        predict_script_path = os.path.join(os.path.dirname(__file__), "predict_error_line.py")
        with open(predict_script_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换模型导入
        content = self.replace_model_imports(content, config["model"])

        # 替换模型实例化
        content = self.replace_model_instantiation(content, config["model"], config["attention_factor"])

        # 替换数据路径
        content = self.replace_data_paths(content, "predict")

        # 修改输出路径
        content = self.modify_output_paths(content, predict_output_dir, "predict")

        # 修改模型加载路径 - 使用字符串替换避免转义问题
        content = content.replace('torch.load("model_final.pth"',
                                f'torch.load("{train_output_dir}/model_final.pth"')

        # 保存生成的脚本到yuce_auto目录，而不是子文件夹
        script_path = f"predict_generated_{config['name'].replace(' ', '_')}.py"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return script_path

    def is_important_output(self, line):
        """判断是否为重要输出信息"""
        important_keywords = [
            "Epoch", "Loss:", "SMAPE:", "训练集大小:", "批次数量:",
            "开始训练", "训练完成", "模型保存", "最佳SMAPE", "总耗时:",
            "验证集大小:", "MSE:", "MAE:", "R²:", "平均相对误差:",
            "Error", "Exception", "Traceback"
        ]
        return any(keyword in line for keyword in important_keywords)

    def run_process_with_realtime_output(self, command, process_name):
        """运行进程并实时显示输出"""
        try:
            # 启动进程
            yuce_auto_dir = os.path.dirname(__file__)
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True,
                encoding='utf-8',
                errors='ignore',
                cwd=yuce_auto_dir,
                bufsize=1,  # 行缓冲
                universal_newlines=True
            )

            # 实时读取输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # 显示实时输出到日志区域
                    output_line = output.strip()
                    if output_line:  # 只显示非空行
                        # 过滤和格式化重要信息
                        if self.is_important_output(output_line):
                            self.log_message(f"📊 {output_line}")
                        else:
                            self.log_message(f"   {output_line}")
                        # 强制更新GUI
                        self.root.update_idletasks()

            # 等待进程完成
            return_code = process.poll()

            if return_code == 0:
                return True
            else:
                self.log_message(f"[{process_name}] 进程失败，返回码: {return_code}")
                return False

        except Exception as e:
            self.log_message(f"[{process_name}] 执行出错: {str(e)}")
            return False

    def run_single_experiment(self, exp_key, config):
        """运行单个实验"""
        try:
            # 生成实验文件夹名
            exp_name = self.generate_experiment_name(config)

            # 创建输出目录
            train_output_dir = os.path.join("train_results", exp_name)
            predict_output_dir = os.path.join("prediction_results", exp_name)

            os.makedirs(train_output_dir, exist_ok=True)
            os.makedirs(predict_output_dir, exist_ok=True)
            os.makedirs(os.path.join(train_output_dir, "checkpoints"), exist_ok=True)

            # 保存实验配置
            self.save_experiment_config(config, train_output_dir)
            self.save_experiment_config(config, predict_output_dir)

            self.log_message(f"开始实验: {config['name']} ({exp_name})")

            # 生成训练脚本
            self.log_message("生成训练脚本...")
            train_script = self.generate_train_script(config, train_output_dir)

            # 执行训练
            self.log_message("开始训练...")
            self.current_exp_label.config(text=f"正在训练: {config['name']} - {config['model']}")

            # 使用实时输出运行训练
            success = self.run_process_with_realtime_output(
                ["python", train_script],
                f"训练-{config['name']}"
            )

            if not success:
                raise Exception("训练失败")

            self.log_message("训练完成，开始预测...")

            # 生成预测脚本
            predict_script = self.generate_predict_script(config, train_output_dir, predict_output_dir)

            # 执行预测
            self.current_exp_label.config(text=f"正在预测: {config['name']} - {config['model']}")

            # 使用实时输出运行预测
            success = self.run_process_with_realtime_output(
                ["python", predict_script],
                f"预测-{config['name']}"
            )

            if not success:
                raise Exception("预测失败")

            self.log_message(f"实验 {config['name']} 完成!")

            # 清理临时生成的脚本文件
            try:
                if os.path.exists(train_script):
                    os.remove(train_script)
                if os.path.exists(predict_script):
                    os.remove(predict_script)
            except:
                pass  # 忽略清理错误

            return True

        except Exception as e:
            self.log_message(f"实验 {config['name']} 失败: {str(e)}")

            # 询问用户如何处理
            result = messagebox.askyesnocancel(
                "实验失败",
                f"实验 {config['name']} 失败:\n{str(e)}\n\n"
                "是否继续下一个实验?\n"
                "是: 继续\n"
                "否: 停止所有实验\n"
                "取消: 重试当前实验"
            )

            if result is True:  # 继续
                return True
            elif result is False:  # 停止
                return False
            else:  # 重试
                return self.run_single_experiment(exp_key, config)

    def start_experiments(self):
        """开始批量实验"""
        # 更新配置
        if not self.update_experiments_from_ui():
            return

        # 验证配置
        if not self.validate_experiments():
            return

        # 确认开始
        if not messagebox.askyesno("确认开始", "确定要开始批量实验吗？这可能需要很长时间。"):
            return

        # 设置UI状态
        self.is_running = True
        self.start_button.config(state="disabled")
        self.pause_button.config(state="normal")
        self.stop_button.config(state="normal")

        # 重置进度
        self.current_experiment = 0
        self.overall_progress.config(maximum=len(self.experiments))
        self.overall_progress.config(value=0)

        # 在新线程中运行实验
        self.experiment_thread = threading.Thread(target=self.run_experiments_thread)
        self.experiment_thread.daemon = True
        self.experiment_thread.start()

    def run_experiments_thread(self):
        """在线程中运行实验"""
        try:
            total_experiments = len(self.experiments)

            for i, (exp_key, config) in enumerate(self.experiments.items()):
                if not self.is_running:
                    break

                self.current_experiment = i + 1
                self.overall_progress.config(value=self.current_experiment)

                # 运行单个实验
                success = self.run_single_experiment(exp_key, config)

                if not success:  # 用户选择停止
                    break

            # 实验完成
            if self.is_running:
                self.log_message("所有实验完成!")
                messagebox.showinfo("完成", "所有实验已完成!")

        except Exception as e:
            self.log_message(f"批量实验出错: {str(e)}")
            messagebox.showerror("错误", f"批量实验出错: {str(e)}")

        finally:
            # 恢复UI状态
            self.is_running = False
            self.start_button.config(state="normal")
            self.pause_button.config(state="disabled")
            self.stop_button.config(state="disabled")
            self.current_exp_label.config(text="实验完成")

    def pause_experiments(self):
        """暂停实验"""
        self.is_running = False
        self.log_message("实验已暂停")

    def stop_experiments(self):
        """停止实验"""
        if messagebox.askyesno("确认停止", "确定要停止所有实验吗？"):
            self.is_running = False
            if self.process:
                self.process.terminate()
            self.log_message("实验已停止")

    def save_config(self):
        """保存配置到文件"""
        if not self.update_experiments_from_ui():
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.experiments, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """从文件加载配置"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    loaded_experiments = json.load(f)

                # 更新实验配置
                self.experiments.update(loaded_experiments)

                # 更新UI
                self.update_ui_from_experiments()

                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认重置", "确定要重置为默认配置吗？"):
            self.init_default_experiments()
            self.update_ui_from_experiments()

    def update_ui_from_experiments(self):
        """从实验配置更新UI"""
        for exp_key, config in self.experiments.items():
            if exp_key in self.config_widgets:
                self.config_widgets[exp_key]["model"].set(config["model"])
                self.config_widgets[exp_key]["batch_size"].set(str(config["batch_size"]))
                self.config_widgets[exp_key]["num_epochs"].set(str(config["num_epochs"]))
                self.config_widgets[exp_key]["learning_rate"].set(str(config["learning_rate"]))
                self.config_widgets[exp_key]["weight_decay"].set(str(config["weight_decay"]))
                self.config_widgets[exp_key]["attention_factor"].set(str(config["attention_factor"]))
                self.config_widgets[exp_key]["description"].set(config["description"])

    def view_results(self):
        """查看实验结果"""
        # 这里可以打开结果文件夹或启动结果分析器
        if os.path.exists("prediction_results"):
            os.startfile("prediction_results")  # Windows
        else:
            messagebox.showinfo("提示", "还没有实验结果")

    def generate_report(self):
        """生成实验报告"""
        try:
            # 导入并运行结果分析器
            from experiment_analyzer import ExperimentAnalyzer
            analyzer = ExperimentAnalyzer()
            analyzer.generate_comprehensive_report()
            messagebox.showinfo("成功", "实验报告已生成")
        except ImportError:
            messagebox.showwarning("提示", "结果分析器尚未实现")
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {str(e)}")

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = BatchExperimentManager()
    app.run()
