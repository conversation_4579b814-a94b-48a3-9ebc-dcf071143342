#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量实验启动脚本 - Batch Experiments Launcher
==========================================

这是批量实验管理系统的启动入口

使用方法：
1. 直接运行此脚本启动GUI界面
2. 或者从命令行运行: python run_batch_experiments.py

作者：AI Assistant
创建时间：2025-01-18
"""

import sys
import os

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

try:
    from batch_experiment_manager import BatchExperimentManager
    
    def main():
        """主函数"""
        print("=" * 60)
        print("🚀 批量实验管理系统")
        print("=" * 60)
        print("正在启动GUI界面...")
        
        # 创建并运行应用
        app = BatchExperimentManager()
        app.run()
        
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有必要的文件都在当前目录中")
    input("按回车键退出...")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    input("按回车键退出...")
