import torch
import torch.nn as nn
import torch.nn.functional as F


class ConvGNReLU(nn.Sequential):
    def __init__(self, in_c, out_c, k=3, s=1, p=1, g=8):
        super().__init__(
            nn.Conv2d(in_c, out_c, k, s, p, bias=False),
            nn.GroupNorm(g, out_c),
            nn.ReLU(inplace=True)
        )


class UNet2D(nn.Module):
    def __init__(self, in_channels=5, base_c=32):
        super().__init__()
        # 编码器（下采样路径）
        self.enc1 = nn.Sequential(ConvGNReLU(in_channels, base_c),
                                  ConvGNReLU(base_c, base_c))
        self.enc2 = nn.Sequential(ConvGNReLU(base_c, base_c * 2),
                                  ConvGNReLU(base_c * 2, base_c * 2))
        self.enc3 = nn.Sequential(ConvGNReLU(base_c * 2, base_c * 4),
                                  ConvGNReLU(base_c * 4, base_c * 4))

        # 解码器（上采样路径） - 修正通道数
        self.dec2 = nn.Sequential(ConvGNReLU(base_c * 6, base_c * 2),  # 输入通道修正为拼接后的6*base_c
                                  ConvGNReLU(base_c * 2, base_c * 2))
        self.dec1 = nn.Sequential(ConvGNReLU(base_c * 3, base_c),  # 输入通道修正为拼接后的3*base_c
                                  ConvGNReLU(base_c, base_c))

        # 输出头部 - 全局池化和全连接层
        self.pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Linear(base_c, 44)  # 输出44个值

    def forward(self, x):
        # 编码器路径
        e1 = self.enc1(x)  # (B, base_c, H, W)
        e1_pool = F.max_pool2d(e1, 2)  # (B, base_c, H/2, W/2)
        e2 = self.enc2(e1_pool)  # (B, 2*base_c, H/2, W/2)
        e2_pool = F.max_pool2d(e2, 2)  # (B, 2*base_c, H/4, W/4)
        e3 = self.enc3(e2_pool)  # (B, 4*base_c, H/4, W/4)

        # 解码器路径 - 使用插值上采样并指定目标尺寸
        d2 = F.interpolate(e3, size=e2.shape[2:], mode='bilinear', align_corners=True)  # 上采样到e2的尺寸
        d2 = torch.cat([d2, e2], dim=1)  # 通道维度拼接 (B, 6*base_c, H/2, W/2)
        d2 = self.dec2(d2)  # (B, 2*base_c, H/2, W/2)

        d1 = F.interpolate(d2, size=e1.shape[2:], mode='bilinear', align_corners=True)  # 上采样到e1的尺寸
        d1 = torch.cat([d1, e1], dim=1)  # 通道维度拼接 (B, 3*base_c, H, W)
        d1 = self.dec1(d1)  # (B, base_c, H, W)

        v = self.pool(d1).flatten(1)  # (B, base_c)
        out = self.fc(v).view(-1, 2, 11, 2)  # (B, 2, 11, 2)
        return out


# 添加工厂函数
def UNet_2D(in_channels=5, base_c=32):
    return UNet2D(in_channels=in_channels, base_c=base_c)

# 
# if __name__ == "__main__":
#     # 创建模型实例 (5通道输入)
#     model = UNet_2D(in_channels=5)
#     print("修复后的2D UNet模型创建成功!")
# 
#     # 创建模拟输入 (batch_size=1, channels=5, height=270, width=270)
#     input_tensor = torch.randn(1, 5, 270, 270)
#     print("输入张量形状:", input_tensor.shape)
# 
#     # 前向传播
#     output = model(input_tensor)
#     print("输出张量形状:", output.shape)
# 
#     # 检查参数数量
#     total_params = sum(p.numel() for p in model.parameters())
#     print(f"模型总参数数量: {total_params:,}")
    
# # 在train.py中
# from unet2d import UNet_2D  # 导入UNet工厂函数
#
# # 替换原来的模型初始化
# model = UNet_2D(
#     in_channels=5,  # 5通道输入
#     base_c=32       # 基础通道数
# ).to(DEVICE)       