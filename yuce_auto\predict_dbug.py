import os
import torch
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
from dataset import VoxelDatasetFromFileName
from configpar import DEVICE, VAL_PATH, MODEL1, BATCH_SIZE, SMAPE_EPS

SCALE_DISP = 100

def smape(pred, true, eps=SMAPE_EPS):
    pred = pred.view(-1)
    true = true.view(-1)
    return 1 - torch.abs(pred - true) / (torch.abs(pred) + torch.abs(true) + eps)

def stiffness_mse(pred, true):
    return torch.mean((pred - true) ** 2)

def validate():
    model = MODEL1.to(DEVICE)
    model.load_state_dict(torch.load("model_final.pth", map_location=DEVICE))
    model.eval()

    val_dataset = VoxelDatasetFromFileName(VAL_PATH)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)

    preds = []
    trues = []
    smape_scores = []
    file_paths = val_dataset.files

    smape_bins = {
        '0-60': [],
        '60-70': [],
        '70-80': [],
        '80-85': [],
    }

    with torch.no_grad():
        for i, (vox, label) in enumerate(val_loader):
            vox = vox.to(DEVICE)
            label = label.to(DEVICE)

            pred_scaled = model(vox)
            pred = pred_scaled * SCALE_DISP
            true = label * SCALE_DISP

            preds.append(pred.item())
            trues.append(true.item())

            smape_val = smape(pred, true).item()
            smape_scores.append(smape_val)

            if smape_val < 0.85:
                filename = os.path.basename(file_paths[i])
                smape_percent = smape_val * 100

                if smape_percent < 60:
                    smape_bins['0-60'].append((filename, smape_percent))
                elif smape_percent < 70:
                    smape_bins['60-70'].append((filename, smape_percent))
                elif smape_percent < 80:
                    smape_bins['70-80'].append((filename, smape_percent))
                else:
                    smape_bins['80-85'].append((filename, smape_percent))

    preds = np.array(preds)
    trues = np.array(trues)

    if len(trues) == 0:
        print("❌ No validation data collected. Please check VAL_PATH and your dataset format.")
        return

    mse = np.mean((preds - trues) ** 2)
    avg_smape = 1 - np.mean(np.abs(preds - trues) / (np.abs(preds) + np.abs(trues) + SMAPE_EPS))

    print(f"Validation MSE: {mse:.6f}")
    print(f"Validation SMAPE: {avg_smape:.6f}")

    plt.figure(figsize=(6, 6))
    plt.scatter(trues, preds, alpha=0.6)
    plt.plot([trues.min(), trues.max()], [trues.min(), trues.max()], 'r--', lw=2)
    plt.xlabel("True Stiffness")
    plt.ylabel("Predicted Stiffness")
    plt.title("Validation: True vs Predicted Stiffness")
    plt.grid(True)
    plt.savefig("validation_scatter.png", dpi=200)
    print("📈 Scatter plot saved as validation_scatter.png")

    # 输出并写入文本文件
    txt_lines = ["Files with SMAPE < 85% grouped by accuracy range:\n"]
    for k, v in smape_bins.items():
        txt_lines.append(f"\nSMAPE {k}%:")
        if not v:
            txt_lines.append("  (None)")
        for fname, score in v:
            txt_lines.append(f"  {fname}: {score:.2f}%")

    result_txt = "\n".join(txt_lines)
    print("\n🔍 " + result_txt)

    # 写入 txt 文件
    with open("low_accuracy_samples.txt", "w", encoding="utf-8") as f:
        f.write(result_txt)
    print("📄 Low-accuracy sample info saved to low_accuracy_samples.txt")


if __name__ == "__main__":
    validate()
