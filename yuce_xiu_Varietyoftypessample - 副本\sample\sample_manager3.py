#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件采样脚本 - File Sampling Script (优化版)
===========================================

功能概述：
1. 一次性选择多个一级文件夹
2. 从某个一级文件夹中选择二级文件夹作为基准
3. 自动匹配其他一级文件夹中的同名二级文件夹
4. 为每个二级文件夹指定采样数量
5. 随机采样文件并复制到目标路径，按二级文件夹分类

输出结构：
指定目录/二级文件夹名/来自各个一级文件夹的随机文件

依赖说明：
- Python 3.x （内置库，无需额外安装）
- tkinter （GUI界面，Python内置）
- os, shutil, random （文件操作，Python内置）

运行方式：
python file_sampling_script.py

注意事项：
- 请确保有足够的磁盘空间存储采样文件
- 采样过程中请勿移动或删除源文件夹
- 建议在运行前备份重要数据
"""

import os
import shutil
import random
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
from tkinter import ttk
from collections import defaultdict


class FileSamplingApp:
    def __init__(self):
        """
        初始化应用程序
        这是我们程序的起点，设置一些基本变量
        """
        self.root = tk.Tk()
        self.root.title("文件采样工具")
        self.root.geometry("600x400")

        # 存储用户选择的文件夹路径
        self.primary_folders = []  # 一级文件夹列表
        self.selected_secondary_folders = []  # 用户选择的二级文件夹名称
        self.sampling_config = {}  # 采样配置：{二级文件夹名: 采样数量}

        self.setup_ui()

    def setup_ui(self):
        """
        设置用户界面
        创建按钮和显示区域，让用户可以点击操作
        """
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 步骤1：选择一级文件夹
        ttk.Label(main_frame, text="步骤1：选择一级文件夹", font=("Arial", 12, "bold")).grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Button(main_frame, text="选择一级文件夹", command=self.select_primary_folders).grid(row=1, column=0, sticky=tk.W,
                                                                                         pady=5)

        # 显示已选择的一级文件夹
        self.primary_folders_text = tk.Text(main_frame, height=5, width=70)
        self.primary_folders_text.grid(row=2, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))

        # 步骤2：选择二级文件夹
        ttk.Label(main_frame, text="步骤2：选择二级文件夹（作为基准）", font=("Arial", 12, "bold")).grid(row=3, column=0, sticky=tk.W,
                                                                                         pady=5)
        ttk.Button(main_frame, text="选择二级文件夹", command=self.select_secondary_folders).grid(row=4, column=0, sticky=tk.W,
                                                                                           pady=5)

        # 显示已选择的二级文件夹
        self.secondary_folders_text = tk.Text(main_frame, height=3, width=70)
        self.secondary_folders_text.grid(row=5, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))

        # 步骤3：设置采样数量
        ttk.Label(main_frame, text="步骤3：设置采样数量", font=("Arial", 12, "bold")).grid(row=6, column=0, sticky=tk.W, pady=5)
        ttk.Button(main_frame, text="设置采样数量", command=self.set_sampling_config).grid(row=7, column=0, sticky=tk.W,
                                                                                     pady=5)

        # 步骤4：开始采样
        ttk.Label(main_frame, text="步骤4：开始采样", font=("Arial", 12, "bold")).grid(row=8, column=0, sticky=tk.W, pady=5)
        ttk.Button(main_frame, text="开始采样", command=self.start_sampling).grid(row=9, column=0, sticky=tk.W, pady=5)

        # 配置网格权重，让界面可以调整大小
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

    def select_primary_folders(self):
        """
        步骤1：一次性选择多个一级文件夹
        使用自定义的多选文件夹对话框
        """
        # 清空之前的选择
        self.primary_folders = []
        self.primary_folders_text.delete(1.0, tk.END)

        # 创建多选文件夹窗口
        self.show_multi_folder_selection()

    def show_multi_folder_selection(self):
        """
        显示多选文件夹对话框
        允许用户一次性选择多个一级文件夹
        """
        # 创建选择窗口
        selection_window = tk.Toplevel(self.root)
        selection_window.title("选择一级文件夹")
        selection_window.geometry("700x500")

        # 创建主框架
        main_frame = ttk.Frame(selection_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 说明文字
        ttk.Label(main_frame, text="请选择一级文件夹（可多选）：", font=("Arial", 12, "bold")).pack(pady=5)

        # 创建框架包含路径输入和浏览按钮
        path_frame = ttk.Frame(main_frame)
        path_frame.pack(fill=tk.X, pady=5)

        ttk.Label(path_frame, text="当前路径：").pack(side=tk.LEFT)
        self.current_path_var = tk.StringVar()
        self.current_path_var.set(os.getcwd())  # 设置初始路径为当前工作目录

        path_entry = ttk.Entry(path_frame, textvariable=self.current_path_var, width=50)
        path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        ttk.Button(path_frame, text="浏览", command=self.browse_for_path).pack(side=tk.RIGHT, padx=5)

        # 创建文件夹列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 创建列表框和滚动条
        self.folder_listbox = tk.Listbox(list_frame, selectmode=tk.MULTIPLE, height=15)
        scrollbar = tk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.folder_listbox.yview)
        self.folder_listbox.configure(yscrollcommand=scrollbar.set)

        self.folder_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)

        ttk.Button(button_frame, text="刷新文件夹列表", command=self.refresh_folder_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="全选", command=self.select_all_folders).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消全选", command=self.deselect_all_folders).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="确定", command=lambda: self.confirm_folder_selection(selection_window)).pack(
            side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=selection_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 初始化文件夹列表
        self.refresh_folder_list()

    def browse_for_path(self):
        """
        浏览选择路径
        """
        folder = filedialog.askdirectory(title="选择文件夹", initialdir=self.current_path_var.get())
        if folder:
            self.current_path_var.set(folder)
            self.refresh_folder_list()

    def refresh_folder_list(self):
        """
        刷新文件夹列表
        显示当前路径下的所有文件夹
        """
        self.folder_listbox.delete(0, tk.END)
        current_path = self.current_path_var.get()

        try:
            # 获取当前路径下的所有文件夹
            items = os.listdir(current_path)
            folders = [item for item in items if os.path.isdir(os.path.join(current_path, item))]
            folders.sort()  # 按字母顺序排序

            # 添加到列表框
            for folder in folders:
                self.folder_listbox.insert(tk.END, folder)

        except Exception as e:
            messagebox.showerror("错误", f"无法读取路径 {current_path}: {str(e)}")

    def select_all_folders(self):
        """
        全选所有文件夹
        """
        self.folder_listbox.select_set(0, tk.END)

    def deselect_all_folders(self):
        """
        取消全选
        """
        self.folder_listbox.selection_clear(0, tk.END)

    def confirm_folder_selection(self, selection_window):
        """
        确认文件夹选择
        """
        selections = self.folder_listbox.curselection()
        if not selections:
            messagebox.showwarning("警告", "请至少选择一个文件夹！")
            return

        current_path = self.current_path_var.get()
        self.primary_folders = []

        # 获取选择的文件夹完整路径
        for i in selections:
            folder_name = self.folder_listbox.get(i)
            folder_path = os.path.join(current_path, folder_name)
            self.primary_folders.append(folder_path)

        # 更新显示
        self.primary_folders_text.delete(1.0, tk.END)
        for folder in self.primary_folders:
            self.primary_folders_text.insert(tk.END, f"{folder}\n")

        selection_window.destroy()
        messagebox.showinfo("成功", f"已选择 {len(self.primary_folders)} 个一级文件夹")

    def get_secondary_folders(self, primary_folder):
        """
        获取指定一级文件夹下的所有二级文件夹

        参数：
        primary_folder: 一级文件夹路径

        返回：
        二级文件夹名称列表
        """
        try:
            # 列出一级文件夹下的所有内容
            items = os.listdir(primary_folder)
            # 过滤出文件夹（排除文件）
            secondary_folders = [item for item in items
                                 if os.path.isdir(os.path.join(primary_folder, item))]
            return secondary_folders
        except Exception as e:
            messagebox.showerror("错误", f"无法读取文件夹 {primary_folder}: {str(e)}")
            return []

    def select_secondary_folders(self):
        """
        步骤2：选择二级文件夹作为基准
        用户从某个一级文件夹中选择二级文件夹
        """
        # 检查是否已选择一级文件夹
        if not self.primary_folders:
            messagebox.showwarning("警告", "请先选择一级文件夹！")
            return

        # 让用户选择从哪个一级文件夹中选择二级文件夹
        folder_names = [os.path.basename(folder) for folder in self.primary_folders]

        # 创建选择窗口
        selection_window = tk.Toplevel(self.root)
        selection_window.title("选择基准一级文件夹")
        selection_window.geometry("400x300")

        tk.Label(selection_window, text="请选择一个一级文件夹作为基准：").pack(pady=10)

        # 创建列表框显示一级文件夹
        listbox = tk.Listbox(selection_window, height=10)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for i, folder in enumerate(self.primary_folders):
            listbox.insert(tk.END, f"{i + 1}. {os.path.basename(folder)}")

        selected_primary = None

        def on_select():
            nonlocal selected_primary
            selection = listbox.curselection()
            if selection:
                selected_primary = self.primary_folders[selection[0]]
                selection_window.destroy()

        ttk.Button(selection_window, text="确定", command=on_select).pack(pady=10)

        # 等待用户选择
        self.root.wait_window(selection_window)

        if not selected_primary:
            messagebox.showwarning("警告", "请选择一个一级文件夹！")
            return

        # 获取选择的一级文件夹下的二级文件夹
        secondary_folders = self.get_secondary_folders(selected_primary)

        if not secondary_folders:
            messagebox.showwarning("警告", "选择的一级文件夹下没有二级文件夹！")
            return

        # 让用户选择二级文件夹
        self.show_secondary_folder_selection(secondary_folders)

    def show_secondary_folder_selection(self, secondary_folders):
        """
        显示二级文件夹选择窗口
        让用户多选二级文件夹

        参数：
        secondary_folders: 二级文件夹名称列表
        """
        # 创建选择窗口
        selection_window = tk.Toplevel(self.root)
        selection_window.title("选择二级文件夹")
        selection_window.geometry("500x400")

        tk.Label(selection_window, text="请选择二级文件夹（可多选）：").pack(pady=10)

        # 创建框架包含列表和滚动条
        frame = tk.Frame(selection_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建列表框，支持多选
        listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, height=15)
        scrollbar = tk.Scrollbar(frame, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 添加二级文件夹到列表
        for folder in secondary_folders:
            listbox.insert(tk.END, folder)

        def on_confirm():
            # 获取用户选择的二级文件夹
            selections = listbox.curselection()
            if not selections:
                messagebox.showwarning("警告", "请至少选择一个二级文件夹！")
                return

            # 保存选择的二级文件夹名称
            self.selected_secondary_folders = [secondary_folders[i] for i in selections]

            # 更新显示
            self.secondary_folders_text.delete(1.0, tk.END)
            for folder in self.selected_secondary_folders:
                self.secondary_folders_text.insert(tk.END, f"{folder}\n")

            selection_window.destroy()
            messagebox.showinfo("成功", f"已选择 {len(self.selected_secondary_folders)} 个二级文件夹")

        ttk.Button(selection_window, text="确定", command=on_confirm).pack(pady=10)

    def count_files_in_secondary_folders(self):
        """
        计算每个二级文件夹类型的文件总数
        遍历所有一级文件夹，统计同名二级文件夹下的文件数量

        返回：
        字典 {二级文件夹名: 文件总数}
        """
        file_counts = defaultdict(int)  # 默认值为0的字典

        # 遍历每个一级文件夹
        for primary_folder in self.primary_folders:
            # 遍历每个选择的二级文件夹
            for secondary_folder in self.selected_secondary_folders:
                secondary_path = os.path.join(primary_folder, secondary_folder)

                # 检查二级文件夹是否存在
                if os.path.exists(secondary_path) and os.path.isdir(secondary_path):
                    try:
                        # 计算该二级文件夹下的文件数量
                        files = [f for f in os.listdir(secondary_path)
                                 if os.path.isfile(os.path.join(secondary_path, f))]
                        file_counts[secondary_folder] += len(files)
                    except Exception as e:
                        print(f"无法读取文件夹 {secondary_path}: {str(e)}")

        return file_counts

    def set_sampling_config(self):
        """
        步骤3：设置每个二级文件夹的采样数量
        """
        # 检查前置条件
        if not self.primary_folders:
            messagebox.showwarning("警告", "请先选择一级文件夹！")
            return

        if not self.selected_secondary_folders:
            messagebox.showwarning("警告", "请先选择二级文件夹！")
            return

        # 计算每个二级文件夹类型的文件总数
        file_counts = self.count_files_in_secondary_folders()

        # 检查是否有文件
        if not file_counts:
            messagebox.showwarning("警告", "选择的二级文件夹中没有找到文件！")
            return

        # 创建设置窗口
        config_window = tk.Toplevel(self.root)
        config_window.title("设置采样数量")
        config_window.geometry("500x400")

        tk.Label(config_window, text="为每个二级文件夹设置采样数量：", font=("Arial", 12, "bold")).pack(pady=10)

        # 创建滚动框架
        canvas = tk.Canvas(config_window)
        scrollbar = tk.Scrollbar(config_window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 存储输入框的引用
        entries = {}

        # 为每个二级文件夹创建输入框
        for i, folder in enumerate(self.selected_secondary_folders):
            frame = tk.Frame(scrollable_frame)
            frame.pack(fill=tk.X, padx=10, pady=5)

            # 显示文件夹名称和文件总数
            tk.Label(frame, text=f"{folder}（总计：{file_counts[folder]}个文件）:").pack(side=tk.LEFT)

            # 创建输入框
            entry = tk.Entry(frame, width=10)
            entry.pack(side=tk.RIGHT, padx=5)
            entries[folder] = entry

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        def on_confirm():
            # 获取用户输入的采样数量
            try:
                new_config = {}
                for folder, entry in entries.items():
                    value = entry.get().strip()
                    if not value:
                        messagebox.showwarning("警告", f"请为 {folder} 设置采样数量！")
                        return

                    sample_count = int(value)
                    if sample_count <= 0:
                        messagebox.showwarning("警告", f"{folder} 的采样数量必须大于0！")
                        return

                    if sample_count > file_counts[folder]:
                        messagebox.showwarning("警告",
                                               f"{folder} 的采样数量（{sample_count}）不能超过总文件数（{file_counts[folder]}）！")
                        return

                    new_config[folder] = sample_count

                # 保存配置
                self.sampling_config = new_config
                config_window.destroy()

                # 显示配置信息
                config_info = "\n".join([f"{folder}: {count}个文件" for folder, count in new_config.items()])
                messagebox.showinfo("成功", f"采样配置已设置：\n{config_info}")

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字！")

        ttk.Button(config_window, text="确定", command=on_confirm).pack(pady=10)

    def start_sampling(self):
        """
        步骤4：开始采样
        根据用户配置，从各个文件夹中采样文件并复制到目标路径
        """
        # 检查前置条件
        if not self.primary_folders:
            messagebox.showwarning("警告", "请先选择一级文件夹！")
            return

        if not self.selected_secondary_folders:
            messagebox.showwarning("警告", "请先选择二级文件夹！")
            return

        if not self.sampling_config:
            messagebox.showwarning("警告", "请先设置采样数量！")
            return

        # 选择目标路径
        target_path = filedialog.askdirectory(title="选择目标路径")
        if not target_path:
            messagebox.showwarning("警告", "请选择目标路径！")
            return

        try:
            # 开始采样过程
            self.perform_sampling(target_path)
            messagebox.showinfo("成功", "文件采样完成！")
        except Exception as e:
            messagebox.showerror("错误", f"采样过程中发生错误：{str(e)}")

    def perform_sampling(self, target_path):
        """
        执行采样过程
        新的输出结构：指定目录/二级文件夹名/来自各个一级文件夹的随机文件
        修改逻辑：确保同一个一级文件夹下各个二级文件夹采样的文件命名一致

        参数：
        target_path: 目标路径
        """
        # 按一级文件夹组织文件结构：{一级文件夹路径: {二级文件夹名: [文件名, ...]}}
        primary_folder_files = {}

        # 遍历所有一级文件夹，收集文件信息
        for primary_folder in self.primary_folders:
            primary_folder_files[primary_folder] = {}

            # 遍历每个选择的二级文件夹
            for secondary_folder in self.selected_secondary_folders:
                secondary_path = os.path.join(primary_folder, secondary_folder)

                # 检查二级文件夹是否存在
                if os.path.exists(secondary_path) and os.path.isdir(secondary_path):
                    try:
                        # 获取该二级文件夹下的所有文件名
                        files = [f for f in os.listdir(secondary_path)
                                 if os.path.isfile(os.path.join(secondary_path, f))]
                        primary_folder_files[primary_folder][secondary_folder] = files
                    except Exception as e:
                        print(f"无法读取文件夹 {secondary_path}: {str(e)}")
                        primary_folder_files[primary_folder][secondary_folder] = []
                else:
                    primary_folder_files[primary_folder][secondary_folder] = []

        # 为每个一级文件夹确定采样的文件名（确保同一级文件夹下各二级文件夹采样文件名一致）
        primary_folder_sampled_files = {}  # {一级文件夹路径: [采样的文件名, ...]}

        for primary_folder in self.primary_folders:
            # 找到该一级文件夹下所有二级文件夹的交集文件（确保所有二级文件夹都有这些文件）
            common_files = None

            for secondary_folder in self.selected_secondary_folders:
                files_in_secondary = set(primary_folder_files[primary_folder][secondary_folder])

                if common_files is None:
                    common_files = files_in_secondary
                else:
                    common_files = common_files.intersection(files_in_secondary)

            # 如果没有交集文件，则从第一个有文件的二级文件夹中选择
            if not common_files:
                for secondary_folder in self.selected_secondary_folders:
                    if primary_folder_files[primary_folder][secondary_folder]:
                        common_files = set(primary_folder_files[primary_folder][secondary_folder])
                        break

            # 从交集文件中随机采样
            if common_files:
                common_files_list = list(common_files)
                # 计算该一级文件夹需要采样的总数（取所有二级文件夹配置的最大值）
                max_sample_count = max(self.sampling_config.values()) if self.sampling_config else 1

                if len(common_files_list) >= max_sample_count:
                    sampled_file_names = random.sample(common_files_list, max_sample_count)
                else:
                    sampled_file_names = common_files_list

                primary_folder_sampled_files[primary_folder] = sampled_file_names
            else:
                primary_folder_sampled_files[primary_folder] = []

        # 根据采样结果复制文件到目标路径
        for secondary_folder, sample_count in self.sampling_config.items():
            # 创建目标二级文件夹
            target_secondary_dir = os.path.join(target_path, secondary_folder)
            os.makedirs(target_secondary_dir, exist_ok=True)

            # 从每个一级文件夹复制对应的采样文件
            for primary_folder in self.primary_folders:
                sampled_files = primary_folder_sampled_files[primary_folder]

                # 只取该二级文件夹需要的采样数量
                files_to_copy = sampled_files[:sample_count]

                for file_name in files_to_copy:
                    source_file_path = os.path.join(primary_folder, secondary_folder, file_name)

                    # 检查源文件是否存在
                    if os.path.exists(source_file_path):
                        target_file_path = os.path.join(target_secondary_dir, file_name)

                        # 处理重名文件：如果目标文件已存在，则添加数字后缀
                        counter = 1
                        original_target_path = target_file_path
                        while os.path.exists(target_file_path):
                            name, ext = os.path.splitext(original_target_path)
                            target_file_path = f"{name}_{counter}{ext}"
                            counter += 1

                        # 复制文件
                        shutil.copy2(source_file_path, target_file_path)
                        print(f"复制文件：{source_file_path} -> {target_file_path}")

    def run(self):
        """
        运行应用程序
        """
        self.root.mainloop()


def main():
    """
    主函数：程序的入口点
    """
    # 创建应用程序实例
    app = FileSamplingApp()

    # 运行应用程序
    app.run()


# 程序入口点
# 当直接运行这个脚本时，会执行main函数
if __name__ == "__main__":
    main()