import os
import torch
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，避免后端报错
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
import matplotlib.font_manager as fm
import pandas as pd  # 用于保存详细结果到CSV
# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimSun']  # 使用宋体替代黑体
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
# 导入新的数据集类和模型
from dataset_tongdao_auto import CarDoorStiffnessDataset  # 替换为您的双通道灰度图数据集类
# from model9 import ResNet_9_2D  # ResNet_9模型
# from model34_new import ResNet_34_2D # ResNet_34模型
# from VGG import VGG_2D  # VGG_2D模型
from u_net import UNet_2D  # UNet_2D模型
# 配置参数 - 根据您的需求调整
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
VAL_PATH = "/home/<USER>/1yaojy/pipei/val"
# VAL_PATH = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\all_sample_hdt_result\try\pipei\val"
BATCH_SIZE = 16  # 批大小
NUM_EPOCHS = 300  # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-5  # 权重衰减
SMAPE_EPS = 1e-6  # SMAPE计算中的小量
MODEL_SAVE_INTERVAL = 50  # 模型保存间隔
ATTENTION_FACTOR = 0.5  # 注意力因子


def smape(pred, true, eps=SMAPE_EPS):
    """计算对称平均绝对百分比误差"""
    pred = pred.view(-1)
    true = true.view(-1)
    return 1 - torch.abs(pred - true) / (torch.abs(pred) + torch.abs(true) + eps)

def stiffness_mse(pred, true):
    return torch.mean((pred - true) ** 2)

def validate():
    # 1. 初始化模型
    model = UNet_2D(
        in_channels=7,  # 双通道输入
        base_c=32  # 基础通道数
    ).to(DEVICE)      # .to(DEVICE)：模型放到显卡上训练
    # 2. 加载训练好的模型权重
    model.load_state_dict(torch.load("model_final.pth", map_location=DEVICE))
    model.eval()# 设置为评估模式
    print("模型加载成功，已设置为评估模式")
    # 3. 准备验证数据集
    val_dataset = CarDoorStiffnessDataset(
        root_dir=VAL_PATH,
        data_file="extracted_data.npy",
        target_size=(270, 270),  # 设置统一尺寸
        padding_mode='constant'
    )
    val_loader = DataLoader(       # 数据加载器
        val_dataset,
        batch_size=1,  # 每个样本单独处理
        shuffle=False,  # 不随机打乱，保持原始顺序
        num_workers=4,  # 根据CPU核心数调整
        pin_memory=True  # 加速GPU数据传输
    )
    print(f"验证集大小: {len(val_dataset)} 样本")
    # 4. 收集预测结果
    preds = []
    trues = []
    # run_ids = []
    img_files = []  # 存储图像文件名
    smape_values = []
    relative_errors = []  # 存储相对误差（(预测-真实)/真实）
    absolute_errors = []  # 存储绝对误差（预测-真实）

    # 创建结果列表，用于保存详细结果
    results = []

    # 对每个输入输出数据进行预测，对比预测值与真实值
    with torch.no_grad(): # 禁用梯度计算，节省内存
        for images, stiffness, img_file, model_id, run_id in val_loader:
            images = images.to(DEVICE)
            stiffness = stiffness.to(DEVICE)

            # 模型预测
            pred_stiffness = model(images)

            # 计算指标
            true_val = stiffness.item()
            pred_val = pred_stiffness.item()
            abs_error = pred_val - true_val  # 绝对误差（预测-真实）
            rel_error = abs_error / true_val  # 相对误差（(预测-真实)/真实）

            # 计算SMAPE
            smape_val = smape(pred_stiffness, stiffness).item()

            # 收集结果，写到列表中
            preds.append(pred_val)  # 预测值
            trues.append(true_val)  # 真实值
            # run_ids.append(run_id[0])  # 因为run_id是元组，取第一个元素
            img_files.append(img_file[0])  # 获取文件名
            smape_values.append(smape_val)  # 对称平均绝对百分比误差
            relative_errors.append(rel_error) # 相对误差（(预测-真实)/真实）
            absolute_errors.append(abs_error)  # 绝对误差（预测-真实）

            # 添加到详细结果列表
            results.append({
                "Image File": img_file[0],  # 使用图像文件名
                "真实刚度": true_val,
                "预测刚度": pred_val,
                "绝对误差": abs_error,
                "相对误差": rel_error,
                "相对误差百分比": rel_error * 100,  # 新增列：相对误差百分比形式
                "SMAPE": smape_val
            })

            # 打印单个样本结果
            print(f"Image File: {img_file[0]} | 真实刚度: {true_val:.4f} | "
                  f"预测刚度: {pred_val:.4f} | 绝对误差: {abs_error:.4f} | "
                  f"相对误差: {rel_error*100:.2f}% | SMAPE: {smape_val:.4f}")
    # 转换为numpy数组
    preds = np.array(preds)
    trues = np.array(trues)
    smape_values = np.array(smape_values)
    relative_errors = np.array(relative_errors)
    absolute_errors = np.array(absolute_errors)

    if len(trues) == 0:
        print("❌ 未收集到验证数据。请检查VAL_PATH和数据集格式。")
        return

    # 5. 计算整体评估指标
    mse = np.mean((preds - trues) ** 2)
    mae = np.mean(np.abs(preds - trues)) # 新增
    smape_val = 1 - np.mean(np.abs(preds - trues) / (np.abs(preds) + np.abs(trues) + SMAPE_EPS)) # 对称平均百分比误差
    avg_smape = np.mean(smape_values)
    r_squared = 1 - np.sum((trues - preds) ** 2) / np.sum((trues - np.mean(trues)) ** 2)

    # 计算在20%误差范围内的样本比例
    within_20_percent = np.sum(np.abs(relative_errors) <= 0.2) / len(relative_errors)

    # 计算相对误差统计量
    # max_rel_error = np.max(relative_errors) * 100  # 转换为百分比
    # min_rel_error = np.min(relative_errors) * 100
    # avg_rel_error = np.mean(relative_errors) * 100
    abs_relative_errors = np.abs(relative_errors)  # 计算相对误差的绝对值
    max_rel_error = np.max(abs_relative_errors) * 100  # 转换为百分比
    min_rel_error = np.min(abs_relative_errors) * 100
    avg_rel_error = np.mean(abs_relative_errors) * 100  # 平均绝对相对误差 (MAPE)
    # 计算绝对误差统计量
    max_abs_error = np.max(absolute_errors)
    min_abs_error = np.min(absolute_errors)
    avg_abs_error = np.mean(absolute_errors)

    print("\n===== 验证结果 =====")
    print(f"MSE: {mse:.6f}")
    print(f"MAE: {mae:.6f}")
    print(f"平均SMAPE: {avg_smape:.6f}")
    print(f"R²: {r_squared:.6f}")
    print(f"Validation SMAPE: {smape_val:.6f}")
    print(f"在20%误差范围内的样本比例: {within_20_percent*100:.2f}%")
    print("\n===== 相对误差统计 =====")
    print(f"最大相对误差: {max_rel_error:.2f}%")
    print(f"最小相对误差: {min_rel_error:.2f}%")
    print(f"平均相对误差: {avg_rel_error:.2f}%")
    print("\n===== 绝对误差统计 =====")
    print(f"最大绝对误差: {max_abs_error:.4f}")
    print(f"最小绝对误差: {min_abs_error:.4f}")
    print(f"平均绝对误差: {avg_abs_error:.4f}")

    # 6. 保存详细结果到CSV文件
    results_df = pd.DataFrame(results)
    results_df.to_csv("validation_results.csv", index=False)
    print("\n详细验证结果已保存至: validation_results.csv")

    # # 7. 绘制预测值与真实值的散点图
    plt.figure(figsize=(10, 10))
    # 创建点是否在20%误差范围内的颜色数组
    colors = np.where(np.abs(relative_errors) <= 0.2, 'green', 'red')
    # 绘制散点图，根据误差范围使用不同颜色
    plt.scatter(trues, preds, c=colors, alpha=0.6, label='predict_spot')

    # 绘制理想对角线
    min_val = min(trues.min(), preds.min())
    max_val = max(trues.max(), preds.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'b--', lw=2, label='ideal_predict')

    # 绘制20%误差边界线
    plt.plot([min_val, max_val], [min_val * 1.2, max_val * 1.2], 'r-', lw=1.5, alpha=0.7, label='+20%error_line')
    plt.plot([min_val, max_val], [min_val * 0.8, max_val * 0.8], 'r-', lw=1.5, alpha=0.7, label='-20%error_line')
    plt.fill_between([min_val, max_val], [min_val * 0.8, max_val * 0.8],
                     [min_val * 1.2, max_val * 1.2], color='gray', alpha=0.1, label='±20%error_area')


    plt.xlabel("True Stiffness", fontsize=12)
    plt.ylabel("Predicted Stiffness", fontsize=12)
    plt.title(f"Validation predict (20%error: {within_20_percent*100:.1f}%)", fontsize=14)
    plt.title("Validation: True vs Predicted Stiffness")
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend(loc='best')

    # 添加统计信息文本框
    stats_text = (f'sample_num: {len(trues)}\n'
                  f'MSE = {mse:.4f}\n'
                  f'MAE = {mae:.4f}\n'
                  f'R² = {r_squared:.4f}\n'
                  f'in 20% range: {within_20_percent*100:.1f}%\n'
                  f'maximum relative error: {max_rel_error:.1f}%\n'
                  f'minimum relative error: {min_rel_error:.1f}%\n'
                  f'average relative error: {avg_rel_error:.1f}%')
    plt.text(0.01, 0.85, stats_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    plt.tight_layout()
    plt.savefig("validation_scatter.png", dpi=200)
    print("\n📈 Scatter plot saved as validation_scatter.png")

if __name__ == "__main__":
    validate()
