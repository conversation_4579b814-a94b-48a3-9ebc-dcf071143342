import time
import os
import torch
import numpy as np
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体支持（如果不需要中文标签可删除）
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
# 导入新的数据集类和模型
from dataset import CarDoorStiffnessDataset  # 替换为您的双通道灰度图数据集类
from model import ResNet_18_2D  # 替换为您的2D ResNet模型

# 配置参数 - 根据您的需求调整
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
TRAIN_PATH = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\R-001\pipei"  # 训练数据路径
BATCH_SIZE = 16  # 批大小
NUM_EPOCHS = 300  # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-5  # 权重衰减
SMAPE_EPS = 1e-6  # SMAPE计算中的小量
MODEL_SAVE_INTERVAL = 50  # 模型保存间隔
ATTENTION_FACTOR = 0.5  # 注意力因子
def train():
    # 1. 准备数据集和数据加载器
    train_dataset = CarDoorStiffnessDataset(
        root_dir=TRAIN_PATH,
        minite_dir="minite_points",
        thickness_dir="other_thickness",
        data_file="extracted_data.npy"
    )

    train_dl = DataLoader(
        train_dataset,
        batch_size=BATCH_SIZE, # 每次训练处理的样本数
        shuffle=True,   # 指从整个数据集获取每个批量图片时需先打乱数据
        num_workers=8,  # 根据您的CPU核心数调整
        pin_memory=True  # 加速GPU数据传输
    )
    print(train_dl)
    for i, batch in enumerate(train_dl):
        # 解包数据，新增两个路径字段
        images, stiffness, run_ids, minite_paths, thickness_paths = batch
        # 打印第一个样本的路径信息
        print(f"\nBatch {i} 的第一个样本路径:")
        print(f"Minite 图像路径: {minite_paths[0]}")
        print(f"Thickness 图像路径: {thickness_paths[0]}")
        # 打印所有样本的路径信息（可选）
        for j in range(len(run_ids)):
            print(f"\n样本 {j} 详细信息:")
            print(f"Run ID: {run_ids[j]}")
            print(f"Minite 路径: {minite_paths[j]}")
            print(f"Thickness 路径: {thickness_paths[j]}")
            print(f"刚度值: {stiffness[j].item()}")

        print("图像张量 shape:", images.shape)  # e.g. torch.Size([5, 2, H, W])
        print("刚度张量 shape:", stiffness.shape, stiffness)  # e.g. torch.Size([5, 1])
        print("run_ids:", run_ids)  # e.g. ['00001', '00003', ...]

if __name__ == "__main__":
    train()