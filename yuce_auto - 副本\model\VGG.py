# vgg2d.py
import torch.nn as nn
import torch
class VGG2D(nn.Module):
    def __init__(self, in_channels=5, attention_factor=0.5):
        super(VGG2D, self).__init__()
        self.attention_factor = attention_factor

        # 特征提取部分
        self.features = nn.Sequential(
            # Block 1
            nn.Conv2d(in_channels, 64, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),  # 270x270 -> 135x135

            # Block 2
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),  # 135x135 -> 67x67

            # Block 3
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),  # 67x67 -> 33x33

            # Block 4
            nn.Conv2d(256, 512, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),  # 33x33 -> 16x16

            # Block 5
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),  # 16x16 -> 8x8
        )
        # 注意力机制（可选）
        self.attention = nn.Sequential(
            nn.Conv2d(512, 512 // 8, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(512 // 8, 512, kernel_size=1),
            nn.Sigmoid()
        ) if attention_factor > 0 else None

        # 分类器（回归器）
        self.classifier = nn.Sequential(
            nn.Flatten(),
            nn.Linear(512 * 8 * 8, 2048),  # 修正输入尺寸
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(2048, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, 1)  # 回归任务输出
        )

    def forward(self, x):
        x = self.features(x)  # 特征提取
        # 应用注意力机制
        if self.attention is not None:
            attention_mask = self.attention(x)
            x = x * (1 + self.attention_factor * attention_mask)
        x = self.classifier(x)  # 分类器
        return x


def VGG_2D(in_channels=5, attention_factor=0.5):
    """创建2D VGG模型实例
    参数:
        in_channels (int): 输入图像的通道数 (默认: 5)
        attention_factor (float): 注意力机制因子 (默认: 0.5
    返回:
        VGG2D: 配置好的VGG模型实例
    """
    return VGG2D(in_channels=in_channels, attention_factor=attention_factor)


# # 验证代码
# if __name__ == "__main__":
#     # 创建模型实例
#     model = VGG_2D(in_channels=5, attention_factor=0.5)
#     print("模型创建成功!")
#
#     # 创建模拟输入 (batch_size=1, channels=5, height=270, width=270)
#     input_tensor = torch.randn(1, 5, 270, 270)
#     print("输入张量形状:", input_tensor.shape)
#
#     # 前向传播
#     output = model(input_tensor)
#     print("输出张量形状:", output.shape)
#     print("输出值:", output.item())
#
#     # 检查参数数量
#     total_params = sum(p.numel() for p in model.parameters())
#     print(f"模型总参数数量: {total_params:,}")