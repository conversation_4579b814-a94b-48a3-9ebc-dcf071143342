import os
import torch
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，适用于服务器或脚本
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
from dataset import VoxelDatasetFromFileName
from configpar import DEVICE, VAL_PATH, MODEL1, BATCH_SIZE, SMAPE_EPS

SCALE_DISP = 100

def validate():
    model = MODEL1.to(DEVICE)
    model.load_state_dict(torch.load("model_final.pth", map_location=DEVICE))
    model.eval()

    val_dataset = VoxelDatasetFromFileName(VAL_PATH)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)

    preds = []
    trues = []

    with torch.no_grad():
        for vox, label in val_loader:
            vox = vox.to(DEVICE)
            label = label.to(DEVICE)

            pred_scaled = model(vox)
            pred = pred_scaled * SCALE_DISP
            true = label * SCALE_DISP

            preds.append(pred.item())
            trues.append(true.item())

    preds = np.array(preds)
    trues = np.array(trues)

    if len(trues) == 0:
        print("❌ No validation data collected. Please check VAL_PATH and your dataset format.")
        return

    # 计算 MSE
    mse = np.mean((preds - trues) ** 2)

    # 计算相对误差准确率
    accuracies = 1 - np.abs(preds - trues) / (np.abs(trues) + SMAPE_EPS)
    mean_accuracy = np.mean(accuracies)

    print(f"Validation MSE: {mse:.6f}")
    print(f"Validation Accuracy (1 - rel_error): {mean_accuracy:.6f}")

    # 绘图：预测 vs 真实
    plt.figure(figsize=(6, 6))
    plt.scatter(trues, preds, alpha=0.6)
    plt.plot([trues.min(), trues.max()], [trues.min(), trues.max()], 'r--', lw=2)
    plt.xlabel("True Stiffness")
    plt.ylabel("Predicted Stiffness")
    plt.title("Validation: True vs Predicted Stiffness")
    plt.grid(True)
    plt.savefig("validation_scatter.png", dpi=200)
    print("📈 Scatter plot saved as validation_scatter.png")

if __name__ == "__main__":
    validate()
