# 批量实验管理系统

## 📋 系统概述

这是一个专为深度学习模型训练设计的批量实验管理系统，可以自动化执行多组实验配置，并生成详细的对比分析报告。

## 🎯 主要功能

### ✅ **批量实验管理**
- 支持同时配置9组不同的实验
- 自动生成训练和预测脚本
- 智能文件命名和结果组织
- 实验进度实时显示

### ✅ **智能代码生成**
- 基于配置自动修改模型导入
- 动态替换训练参数
- 自动管理输出路径

### ✅ **结果分析**
- 性能对比图表
- 参数敏感性分析
- 实验排行榜
- 综合分析报告

## 🚀 快速开始

### 1. 启动系统
```bash
python run_batch_experiments.py
```

### 2. 配置实验
- 在GUI界面中设置9组实验参数
- 选择模型类型：ResNet_18_2D / ResNet_34_2D / ResNet_50_2D
- 调整超参数：批大小、训练轮数、学习率等

### 3. 开始实验
- 点击"🚀 开始批量实验"按钮
- 系统将自动执行所有实验
- 实时查看进度和日志

### 4. 查看结果
- 点击"📊 查看结果"查看输出文件
- 点击"📈 生成报告"创建分析报告

## 📁 文件结构

```
yuce_auto/
├── 🎮 主要脚本
│   ├── run_batch_experiments.py      # 启动脚本
│   ├── batch_experiment_manager.py   # 主控制器
│   └── experiment_analyzer.py        # 结果分析器
│
├── 📊 实验结果
│   ├── train_results/               # 训练结果
│   │   ├── R18_BS16_EP200_LR001/   # 实验1结果
│   │   ├── R34_BS32_EP400_LR0005/  # 实验2结果
│   │   └── ...
│   │
│   ├── prediction_results/          # 预测结果
│   │   ├── R18_BS16_EP200_LR001/
│   │   └── ...
│   │
│   └── experiment_reports/          # 分析报告
│       ├── performance_comparison.png
│       ├── parameter_sensitivity.png
│       ├── experiment_ranking.csv
│       └── experiment_report.txt
│
└── 📋 原始脚本
    ├── train.py                     # 原始训练脚本
    └── predict_error_line.py        # 原始预测脚本
```

## ⚙️ 参数配置说明

### 🔧 **支持的模型**
- **ResNet_18_2D**: 轻量级模型，训练速度快
- **ResNet_34_2D**: 中等复杂度，平衡性能和速度
- **ResNet_50_2D**: 深度模型，性能最佳但训练较慢

### 🔧 **参数范围**
- **批大小**: 8-32
- **训练轮数**: 100, 200, 300, 400, 800
- **学习率**: 0.0001-0.01
- **权重衰减**: 1e-6 到 1e-3
- **注意力因子**: 0.1-1.0

## 📊 结果分析

### 🏆 **性能指标**
- **MSE**: 均方误差（越小越好）
- **MAE**: 平均绝对误差（越小越好）
- **R²**: 决定系数（越大越好）
- **SMAPE**: 对称平均绝对百分比误差（越大越好）
- **20%内准确率**: 预测误差在20%范围内的样本比例

### 📈 **生成的图表**
1. **性能对比图**: 所有实验的关键指标对比
2. **参数敏感性分析**: 参数对性能的影响分析
3. **训练曲线对比**: 不同实验的训练过程对比
4. **实验排行榜**: 按性能排序的详细表格

## 🛠️ 使用技巧

### ✅ **实验设计建议**
1. **基线实验**: 先用默认参数建立基线
2. **单变量实验**: 每次只改变一个参数
3. **对比实验**: 不同模型使用相同参数对比
4. **优化实验**: 基于前期结果优化参数组合

### ✅ **错误处理**
- 实验失败时可选择继续、停止或重试
- 详细的错误日志帮助调试
- 自动保存已完成的实验结果

### ✅ **配置管理**
- 支持保存/加载实验配置
- 可重置为默认配置
- 配置文件采用JSON格式，便于编辑

## 🔍 故障排除

### ❓ **常见问题**

**Q: 实验失败怎么办？**
A: 检查数据路径、GPU内存、依赖库是否正确安装

**Q: 如何修改数据路径？**
A: 直接在原始的train.py中修改TRAIN_PATH和VAL_PATH

**Q: 可以中途停止实验吗？**
A: 可以，点击"🛑 停止"按钮，已完成的实验结果会保留

**Q: 如何查看详细的训练日志？**
A: 在GUI的日志区域可以看到实时输出，详细日志保存在各实验文件夹中

## 📞 技术支持

如果遇到问题，请检查：
1. Python环境和依赖库
2. 数据文件路径和格式
3. GPU内存是否充足
4. 原始train.py和predict_error_line.py是否可正常运行

---

**祝你实验顺利！🎉**
