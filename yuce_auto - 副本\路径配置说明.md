# 📍 路径配置说明

## 🎯 **现在你有两种配置路径的方式**

### 方式1：在GUI界面中配置（推荐）✅

1. **启动批量实验管理器**：
   ```bash
   python run_batch_experiments.py
   ```

2. **在GUI界面顶部的"数据路径配置"区域设置**：
   - **训练数据路径**：点击"浏览"按钮选择训练数据文件夹
   - **验证数据路径**：点击"浏览"按钮选择验证数据文件夹

3. **默认路径**（你可以修改）：
   ```
   训练数据: D:\Aprojecdtdoor\Dataset_Construction\code-v2\yuce_xiu_Varietyoftypessample\sample\all\train
   验证数据: D:\Aprojecdtdoor\Dataset_Construction\code-v2\yuce_xiu_Varietyoftypessample\sample\all\val
   ```

### 方式2：直接修改原始脚本

如果你不想使用GUI配置，可以直接修改：

#### 在 `train.py` 中：
```python
# 找到第23行左右
TRAIN_PATH = r"你的训练数据路径"
```

#### 在 `predict_error_line.py` 中：
```python  
# 找到第21行左右
VAL_PATH = r"你的验证数据路径"
```

## 🔧 **数据文件夹结构要求**

你的数据文件夹应该包含：

```
train/  (或 val/)
├── extracted_data.npy          # 刚度值数据文件
├── cross_sections/            # 通道1：截面图像
│   ├── R_001_00001.png
│   └── ...
├── inner_points/              # 通道2：内板点图像
├── minite_points/             # 通道3：细节点图像  
├── outer_points/              # 通道4：外板点图像
├── rbe2_only/                # 通道5：RBE2连接图像
├── reinforce_B/              # 通道6：加强板B图像
└── weld_only/                # 通道7：焊点图像
```

## ✅ **推荐使用GUI配置的原因**

1. **可视化操作**：点击浏览按钮选择文件夹，不容易出错
2. **实时验证**：系统会检查路径是否存在
3. **统一管理**：所有实验使用相同的数据路径配置
4. **保存配置**：可以保存路径配置供下次使用

## 🚀 **快速开始**

1. 运行 `python run_batch_experiments.py`
2. 在GUI顶部设置正确的训练和验证数据路径
3. 配置9组实验参数
4. 点击"开始批量实验"

就这么简单！🎉
