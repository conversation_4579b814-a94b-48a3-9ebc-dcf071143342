# 车门类型与组件ID映射配置
# 格式: [车门类型] 例如 [001]
# loading_node_ids: 加载点节点ID列表
# weld_comp_ids: 焊点组件ID集合
# glue_comp_ids: 胶组件ID集合
# OUTER_COMP_ID: 外板组件ID
# INNER_COMP_IDS: 内板组件ID集合
# REINFORCE_A_COMP_ID: 腰线加强板A组件ID (无则为none)
# REINFORCE_B_COMP_ID: 腰线加强板B组件ID (无则为none)
# MARKED_COMP_IDS: 标记组件集合
# UPPER_HINGE_REINFORCE_COMP_ID: 上铰链加强板组件ID (无则为none)
# LOWER_HINGE_REINFORCE_COMP_ID: 下铰链加强板组件ID (无则为none)

[006]
loading_node_ids = 51031,51032,52676,52675
weld_comp_ids = 1633
glue_comp_ids = 1681
OUTER_COMP_ID = 1667
INNER_COMP_IDS = 1666
REINFORCE_A_COMP_ID = none
REINFORCE_B_COMP_ID = 1669
MARKED_COMP_IDS = 1619,1666,1667,1668,1669
LOCK_REINFORCE_COMP_ID = 1670
WINDOW_FRAME_COMP_ID = 1668
UPPER_HINGE_REINFORCE_COMP_ID =  1675
LOWER_HINGE_REINFORCE_COMP_ID = 1671

[010]
loading_node_ids = 34502,34503,17052,17051
weld_comp_ids = 828,829
glue_comp_ids = 832
OUTER_COMP_ID = 41
INNER_COMP_IDS = 40
REINFORCE_A_COMP_ID = none
REINFORCE_B_COMP_ID = 42
MARKED_COMP_IDS = 41,40,42,45,54
LOCK_REINFORCE_COMP_ID = 44
WINDOW_FRAME_COMP_ID = 45
UPPER_HINGE_REINFORCE_COMP_ID =  49
LOWER_HINGE_REINFORCE_COMP_ID = 47

[012]
loading_node_ids = 76554,76561,89413,89412
weld_comp_ids = 112
glue_comp_ids = 111
OUTER_COMP_ID = 12
INNER_COMP_IDS = 14
REINFORCE_A_COMP_ID = 30
REINFORCE_B_COMP_ID = 32
MARKED_COMP_IDS = 12,14,30,26,87
LOCK_REINFORCE_COMP_ID = 24
WINDOW_FRAME_COMP_ID = 26
UPPER_HINGE_REINFORCE_COMP_ID =  22
LOWER_HINGE_REINFORCE_COMP_ID = 28

[015]
loading_node_ids = 53418,53419,57364,57370
weld_comp_ids = 2000028
glue_comp_ids = 2000027
OUTER_COMP_ID = 2000012
INNER_COMP_IDS = 2000009
REINFORCE_A_COMP_ID = none
REINFORCE_B_COMP_ID = 2000023
MARKED_COMP_IDS = 2000012,2000009,2000023,2000008,2000010
LOCK_REINFORCE_COMP_ID = 2000020
WINDOW_FRAME_COMP_ID = 2000001,2000007,2000008,2000018
UPPER_HINGE_REINFORCE_COMP_ID =  2000019
LOWER_HINGE_REINFORCE_COMP_ID = none


[017]
loading_node_ids = 30903,30904,27083,27085
weld_comp_ids = 26,28
glue_comp_ids = 27
OUTER_COMP_ID = 5
INNER_COMP_IDS = 2
REINFORCE_A_COMP_ID = none
REINFORCE_B_COMP_ID = 4
MARKED_COMP_IDS = 5,2,4,3,20
LOCK_REINFORCE_COMP_ID = 11
WINDOW_FRAME_COMP_ID = 3
UPPER_HINGE_REINFORCE_COMP_ID =  6
LOWER_HINGE_REINFORCE_COMP_ID = 7


[018]
loading_node_ids = 25183,25182,25166,25165
weld_comp_ids = 6106027,6106029
glue_comp_ids = 6106028
OUTER_COMP_ID = 6101021
INNER_COMP_IDS = 6101041
REINFORCE_A_COMP_ID = none
REINFORCE_B_COMP_ID = 6101361
MARKED_COMP_IDS = 6101021,6101041,6101361,8,6101421
LOCK_REINFORCE_COMP_ID = 6101221
WINDOW_FRAME_COMP_ID = 6101241
UPPER_HINGE_REINFORCE_COMP_ID =  6101181
LOWER_HINGE_REINFORCE_COMP_ID = 6101281


[024]
loading_node_ids = 134569,134568,134710,134711
weld_comp_ids = 108
glue_comp_ids = 111,46
OUTER_COMP_ID = 4
INNER_COMP_IDS = 14
REINFORCE_A_COMP_ID = none
REINFORCE_B_COMP_ID = 12
MARKED_COMP_IDS = 4,14,12,13
LOCK_REINFORCE_COMP_ID = 6
WINDOW_FRAME_COMP_ID = 13
UPPER_HINGE_REINFORCE_COMP_ID =  5
LOWER_HINGE_REINFORCE_COMP_ID = 10


[036]
loading_node_ids = 33510,33511,33346,33347
weld_comp_ids = 197
glue_comp_ids = 83
OUTER_COMP_ID = 1
INNER_COMP_IDS = 3
REINFORCE_A_COMP_ID = none
REINFORCE_B_COMP_ID = 12
MARKED_COMP_IDS = 1,3,12,2,10
LOCK_REINFORCE_COMP_ID = 9
WINDOW_FRAME_COMP_ID = 10
UPPER_HINGE_REINFORCE_COMP_ID =  8
LOWER_HINGE_REINFORCE_COMP_ID = 11