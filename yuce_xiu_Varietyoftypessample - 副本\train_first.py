import time
import os
import torch
import numpy as np
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
# ----------------本训练代码输入数据是三中间板通道，更改后实现数据集的划分
# 设置中文字体支持（如果不需要中文标签可删除）
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
# 导入新的数据集类和模型
from dataset_tongdao import CarDoorStiffnessDataset  # 替换为您的双通道灰度图数据集类
from model import ResNet_18_2D  # 替换为您的2D ResNet模型

# 配置参数 - 根据您的需求调整
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# TRAIN_PATH = "/home/<USER>/1yaojy/pipei/train"  # 在gpu上的路径
TRAIN_PATH = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\R-001-2\pipei\train"
BATCH_SIZE = 16  # 批大小
NUM_EPOCHS = 200  # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-5  # 权重衰减
SMAPE_EPS = 1e-6  # SMAPE计算中的小量
MODEL_SAVE_INTERVAL = 50  # 模型保存间隔
ATTENTION_FACTOR = 0.5  # 注意力因子


def smape_scalar(pred, true, eps=SMAPE_EPS):
    """计算SMAPE指标"""
    pred = pred.view(-1)
    true = true.view(-1)
    return 1 - torch.abs(pred - true) / (torch.abs(pred) + torch.abs(true) + eps)


def train():
    # 1. 准备数据集和数据加载器
    train_dataset = CarDoorStiffnessDataset(
        root_dir=TRAIN_PATH,
        minite_dir="minite_points",
        thickness_dir="other_thickness",
        combination_dir="thickness_combination",
        data_file="extracted_data.npy"
    )

    train_dl = DataLoader(
        train_dataset,
        batch_size=BATCH_SIZE, # 每次训练处理的样本数
        shuffle=True,   # 指从整个数据集获取每个批量图片时需先打乱数据
        num_workers=8,  # 根据您的CPU核心数调整
        pin_memory=True  # 加速GPU数据传输
    )

    print(f"训练集大小: {len(train_dataset)} 样本")
    print(f"批次数量: {len(train_dl)}")

    # 2. 初始化模型，使用model
    model = ResNet_18_2D(
        in_channels=3,  # 三通道输入
        attention_factor=ATTENTION_FACTOR
    ).to(DEVICE)      # .to(DEVICE)：模型放到显卡上训练

    # 3. 设置优化器和学习率调度，使用Adam算法实现梯度下降
    optim = torch.optim.Adam(
        model.parameters(),
        lr=LEARNING_RATE,
        weight_decay=WEIGHT_DECAY
    )
    scheduler = CosineAnnealingLR(optim, T_max=NUM_EPOCHS) # 作用是什么

    # 4. 定义损失函数
    mse_loss_fn = torch.nn.MSELoss()  # 均方误差，更关注大误差。
    mae_loss_fn = torch.nn.L1Loss()  # 平均绝对误差，对离群点更稳健。
    alpha = 0.5  # MSE权重
    beta = 0.5  # MAE权重

    # 5. 创建保存目录
    os.makedirs("checkpoints", exist_ok=True)

    # 6. 训练日志
    loss_log = [] # 存储每个 epoch 的平均 loss
    smape_log = []
    best_smape = float('inf')

    print(f"开始训练... 设备: {DEVICE}")
    start_time = time.time()   # 训练起始时间（统计耗时）

    # 开始训练
    for epoch in range(1, NUM_EPOCHS + 1):  # 训练的循环次数，比如训练1000次
        model.train()
        epoch_loss = 0.0
        epoch_smape = 0.0

        # 批次训练
        for batch in train_dl:
            # 解包数据 (图像, 刚度值, run_id) - 我们只需要前两个
            images, stiffness, _ = batch
            images = images.to(DEVICE)
            stiffness = stiffness.to(DEVICE)

            # 前向传播，导入model中
            pred_stiffness = model(images)

            # 计算损失
            mse = mse_loss_fn(pred_stiffness, stiffness)
            mae = mae_loss_fn(pred_stiffness, stiffness)
            loss = alpha * mse + beta * mae

            # 计算SMAPE
            smape = smape_scalar(pred_stiffness, stiffness).mean()

            # 反向传播和优化
            optim.zero_grad()
            loss.backward()
            optim.step()

            # 更新日志
            epoch_loss += loss.item() # 书上使用所有loss，使用append函数-81页
            epoch_smape += smape.item()

        # 计算平均指标-本代码使用平均，
        avg_loss = epoch_loss / len(train_dl)
        avg_smape = epoch_smape / len(train_dl)

        # 更新学习率
        scheduler.step()

        # 记录日志
        loss_log.append(avg_loss)
        smape_log.append(avg_smape)

        # 打印进度
        print(f"Epoch {epoch:03d}/{NUM_EPOCHS} | "
              f"Loss: {avg_loss:.4e} | "
              f"SMAPE: {avg_smape:.4f} | "
              f"LR: {scheduler.get_last_lr()[0]:.2e}")

        # 定期保存模型
        if epoch % MODEL_SAVE_INTERVAL == 0 or epoch == NUM_EPOCHS:
            save_path = f"checkpoints/model_epoch{epoch}.pth"
            torch.save(model.state_dict(), save_path)
            print(f"模型保存至: {save_path}")

        # 保存最佳模型
        if avg_smape < best_smape:
            best_smape = avg_smape
            torch.save(model.state_dict(), "checkpoints/best_model.pth")
            print(f"新最佳SMAPE: {best_smape:.4f} - 模型已保存")

    # 7. 训练完成
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\n训练完成! 总耗时: {total_time:.2f}秒 ({total_time / 60:.2f}分钟)")

    # 保存最终模型
    torch.save(model.state_dict(), "model_final.pth")
    print("最终模型保存至: model_final.pth")

    # 8. 绘制损失曲线
   # 8. 绘制损失曲线（使用英文标签避免字体问题）
    plt.figure(figsize=(12, 6))

    plt.subplot(1, 2, 1)
    plt.plot(loss_log, label="Training Loss")
    plt.xlabel("Epoch")
    plt.ylabel("Loss")
    plt.title("Training Loss Curve")
    plt.grid(True)
    plt.legend()

    plt.subplot(1, 2, 2)
    plt.plot(smape_log, label="SMAPE", color="orange")
    plt.xlabel("Epoch")
    plt.ylabel("SMAPE")
    plt.title("SMAPE Curve")
    plt.grid(True)
    plt.legend()

    plt.tight_layout()
    plt.savefig("training_metrics.png", dpi=200)
    print("Training metrics saved to: training_metrics.png")


if __name__ == "__main__":
    train()