import torch

DEVICE = 'cuda:0' if torch.cuda.is_available() else 'cpu'
TRAIN_PATH = '../data/train'
VAL_PATH = '../data/val'
# TRAIN_PATH = '../yiqi/result/train'
# VAL_PATH = '../yiqi/result/val'
BATCH_SIZE = 16
NUM_EPOCHS = 400
LEARNING_RATE = 1e-3
WEIGHT_DECAY = 0.01
SMAPE_EPS = 1e-6

from model import model
# from model import model101

MODEL1 = model.ResNet_18_with_attention(in_channels=5, attention_factor=4.0)
# MODEL1 = model34.ResNet3D(block=model34.ResidualBlock3D, layers=[2, 2, 2, 2], in_channels=5)
# MODEL1 = model50.ResNet3D(block=model50.BottleneckBlock3D, layers=[2, 2, 2, 2], in_channels=5)
# MODEL1 = model101.ResNet3D(block=model101.BottleneckBlock3D, layers=[2, 2, 2, 2], in_channels=5)
