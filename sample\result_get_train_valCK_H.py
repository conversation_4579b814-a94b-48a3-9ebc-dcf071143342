import os
import numpy as np
import pandas as pd
import re
import json

# -------------------可以同时获取训练集已经验证集的结果----------------------------------------------------
"""
以处理多个Excel文件，并根据图片文件名中的车型编号选择对应的Excel文件
"""
# 配置参数
# ========================
RESULT_DIR = r'D:\Aprojecdtdoor\Dataset_Construction\data_match\result'  # Excel文件路径
BASE_PATH = r'D:\Aprojecdtdoor\Dataset_Construction\code-v2\sample\CK_H\CK_H_weld_reinB_wf(reinA)_outerp'
TRAIN_TXT = os.path.join(BASE_PATH, 'train_files.txt')  # 训练集图片列表文件TXT
VAL_TXT = os.path.join(BASE_PATH, 'val_files.txt')  # 验证集图片列表文件TXT
COLUMN_TEMPLATE = "../collector/run__{run_id}/1_Global_stif.f06"  # 列名模板

# 输出路径
TRAIN_OUTPUT_NPY = os.path.join(BASE_PATH, 'train', 'extracted_data.npy')
TRAIN_OUTPUT_CSV = os.path.join(BASE_PATH, 'train', 'extracted_data.csv')
VAL_OUTPUT_NPY = os.path.join(BASE_PATH, 'val', 'extracted_data.npy')
VAL_OUTPUT_CSV = os.path.join(BASE_PATH, 'val', 'extracted_data.csv')

# 缓存已加载的Excel文件
excel_cache = {}


# ========================
# 从图片文件名提取run_id
# ========================
def extract_model_run_id(filename):
    """
    从图片文件名中提取车型和run_id
    支持两种格式:
    1. R_006_00002_components_multi.png (下划线分隔)
    2. F-010-00232-components_multi.png (短横线分隔)
    """
    base_name = os.path.basename(filename)

    # 尝试用下划线分隔
    parts = base_name.split('_')
    if len(parts) >= 3:
        model_id = f"{parts[0]}_{parts[1]}"
        run_id = parts[2]
        if re.match(r'^\d{5}$', run_id):
            return model_id, run_id

    # 尝试用短横线分隔
    parts = base_name.split('-')
    if len(parts) >= 3:
        model_id = f"{parts[0]}_{parts[1]}"  # 保持模型ID格式为 F_010
        run_id = parts[2]
        if re.match(r'^\d{5}$', run_id):
            return model_id, run_id

    return None, None


# ========================
# 构建列名列表
# ========================
def build_column_names_from_txt(txt_file):
    """基于图片TXT文件构建列名列表"""
    # 1.读取TXT文件中的所有图片文件名
    with open(txt_file, 'r') as f:
        image_files = [line.strip() for line in f.readlines()]
    print(f"从 {os.path.basename(txt_file)} 文件中读取了 {len(image_files)} 个图片文件名")

    # 2.构建映射: 列名 -> (model_id, run_id, img_file)
    image_mapping = {}
    model_excel_mapping = {}

    for img_file in image_files:
        model_id, run_id = extract_model_run_id(img_file)  # 获取R_006 以及 00002
        if model_id and run_id:
            # 使用模板构建列名，获取列的名字
            col_name = COLUMN_TEMPLATE.format(run_id=run_id)
            # 构建Excel文件路径
            excel_file = f"{model_id}result.xlsx"
            excel_path = os.path.join(RESULT_DIR, excel_file)
            # 存储映射关系
            image_mapping[img_file] = {
                'model_id': model_id,
                'run_id': run_id,
                'col_name': col_name,
                'excel_path': excel_path
            }
            # 记录车型对应的Excel文件
            model_excel_mapping[model_id] = excel_path
        else:
            print(f"警告: 无法从文件名 '{img_file}' 提取run_id")

    # 打印提取结果示例
    print(f"成功构建了 {len(image_mapping)} 个图片映射")
    print("前5个映射关系:")
    for i, (img, meta) in enumerate(list(image_mapping.items())[:5]):
        print(f"  图片: {img}")
        print(f"  车型: {meta['model_id']}, run_id: {meta['run_id']}")
        print(f"  列名: {meta['col_name']}")
        print(f"  Excel: {meta['excel_path']}")

    return image_mapping, model_excel_mapping


# ========================
# 加载Excel文件
# ========================
def load_excel(excel_path):
    """加载Excel文件到缓存"""
    if excel_path in excel_cache:
        return excel_cache[excel_path]

    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在: {excel_path}")
        return None

    try:
        df = pd.read_excel(excel_path, index_col=0)
        excel_cache[excel_path] = df
        print(f"成功加载Excel: {excel_path}")
        return df
    except Exception as e:
        print(f"加载Excel失败 {excel_path}: {str(e)}")
        return None


# ========================
# 处理单个数据集
# ========================
def process_dataset(txt_file, output_npy, output_csv, dataset_name):
    """处理单个数据集（训练集或验证集）"""
    print(f"\n{'=' * 80}")
    print(f"处理 {dataset_name} 数据集")
    print(f"TXT文件: {os.path.basename(txt_file)}")
    print(f"输出NPY: {output_npy}")
    print(f"输出CSV: {output_csv}")
    print('=' * 80)

    # 1.确保输出目录存在
    output_dir = os.path.dirname(output_npy)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")

    # 2.构建图片映射
    image_mapping, model_excel_mapping = build_column_names_from_txt(
        txt_file)  # image_mapping是图片映射字典 model_excel_mapping是车门与结果文件对应字典

    if not image_mapping:
        print(f"{dataset_name}错误: 未构建有效的图片映射!")
        return None

    # 3.收集所有需要处理的Excel文件
    excel_files = set(meta['excel_path'] for meta in image_mapping.values())
    print(f"需要处理的Excel文件: {len(excel_files)} 个")

    # 初始化结果存储
    results = {
        'columns': [],
        'data': [],
        'model_ids': [],
        'run_ids': [],
        'img_files': [],
        'excel_paths': []
    }
    # 4.遍历所有图片文件
    for img_file, meta in image_mapping.items():
        excel_path = meta['excel_path']
        model_id = meta['model_id']
        run_id = meta['run_id']
        col_name = meta['col_name']

        # 加载Excel文件
        df = load_excel(excel_path)
        if df is None:
            print(f"跳过图片 {img_file}, 无法加载Excel")
            continue
        # 检查目标行是否存在
        if 'LOAD_CK_H' not in df.index:
            print(f"Excel中未找到'LOAD_CK_H'行: {excel_path}")
            continue
        # 检查列是否存在
        if col_name not in df.columns:
            print(f"列 {col_name} 不存在于Excel中: {excel_path}")
            continue
        # 提取目标行数据
        target_row = df.loc['LOAD_CK_H']
        value = target_row[col_name]
        # 存储结果
        results['columns'].append(col_name)
        results['data'].append(value)
        results['model_ids'].append(model_id)
        results['run_ids'].append(run_id)
        results['img_files'].append(img_file)
        results['excel_paths'].append(excel_path)

    # 检查是否提取到数据
    if not results['columns']:
        print(f"{dataset_name}错误: 未提取到任何数据!")
        return None

    # 转换为numpy数组
    data_array = np.array(results['data'], dtype=float)  # data中储存是刚度值

    # 创建数据字典
    data_dict = {
        'row_name': 'LOAD_CK_H',
        'dataset': dataset_name,
        'txt_file': txt_file,
        'columns': results['columns'],
        'model_ids': results['model_ids'],
        'run_ids': results['run_ids'],
        'img_files': results['img_files'],
        'excel_paths': results['excel_paths'],
        'data': data_array  # 刚度值
    }
    print("npy数组", data_dict)
    # 保存为npy文件
    np.save(output_npy, data_dict)
    print(f"{dataset_name}数据已保存为 {output_npy}")

    # 保存为CSV用于验证
    result_df = pd.DataFrame({
        'model_id': results['model_ids'],
        'run_id': results['run_ids'],
        'img_file': results['img_files'],
        'excel_path': results['excel_paths'],
        'column_name': results['columns'],
        'LOAD_CK_H': data_array
    })
    result_df.to_csv(output_csv, index=False)
    print(f"{dataset_name}验证数据已保存为 {output_csv}")

    return data_dict


# ========================
# 主处理函数
# ========================
def process_data():
    # 清空缓存
    global excel_cache
    excel_cache = {}

    # 处理训练集
    train_data = process_dataset(
        TRAIN_TXT,
        TRAIN_OUTPUT_NPY,
        TRAIN_OUTPUT_CSV,
        "训练集"
    )
    # 清空缓存
    excel_cache = {}
    # 处理验证集
    val_data = process_dataset(
        VAL_TXT,
        VAL_OUTPUT_NPY,
        VAL_OUTPUT_CSV,
        "验证集"
    )

    return train_data, val_data


# ========================
# 执行主程序
# ========================
if __name__ == "__main__":
    train_data, val_data = process_data()

    print("\n" + "=" * 80)
    print("处理结果摘要:")
    print("=" * 80)

    if train_data:
        print("\n训练集结果:")
        print(f"匹配列数量: {len(train_data['columns'])}")

        # 计算涉及车型数量（修复f-string语法问题）
        model_pairs = [f"{parts[0]}_{parts[1]}" for parts in [mid.split('_') for mid in train_data['model_ids']]]
        unique_models = set(model_pairs)
        print(f"涉及车型数量: {len(unique_models)}")

        print(f"涉及Excel文件: {len(set(train_data['excel_paths']))}")
        print(f"数据点数量: {len(train_data['data'])}")
        print(f"输出位置: {TRAIN_OUTPUT_NPY}")

    if val_data:
        print("\n验证集结果:")
        print(f"匹配列数量: {len(val_data['columns'])}")

        # 计算涉及车型数量（修复f-string语法问题）
        model_pairs_val = [f"{parts[0]}_{parts[1]}" for parts in [mid.split('_') for mid in val_data['model_ids']]]
        unique_models_val = set(model_pairs_val)
        print(f"涉及车型数量: {len(unique_models_val)}")

        print(f"涉及Excel文件: {len(set(val_data['excel_paths']))}")
        print(f"数据点数量: {len(val_data['data'])}")
        print(f"输出位置: {VAL_OUTPUT_NPY}")

    print("\n处理完成!")