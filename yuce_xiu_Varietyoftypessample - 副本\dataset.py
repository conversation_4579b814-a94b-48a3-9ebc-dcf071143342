import os
import numpy as np
import torch
from torch.utils.data import Dataset
from PIL import Image
import re

class CarDoorStiffnessDataset(Dataset):
    def __init__(self, root_dir, minite_dir="minite_points", thickness_dir="other_thickness",
                 data_file="extracted_data.npy", transform=None):
        """
        车门刚度数据集加载器
        参数:
            root_dir: 根目录路径 (包含minite_points和other_thickness文件夹)
            minite_dir: minite_points文件夹名称
            thickness_dir: other_thickness文件夹名称
            data_file: 包含刚度值的npy文件名
            transform: 可选的数据增强/预处理变换
        """
        self.root_dir = root_dir   # 传入的参数 root_dir 赋值给类的实例变量 self.root_dir
        self.transform = transform
        self.samples = []  # 存储样本信息: (minite_path, thickness_path, stiffness)
        self.stiffness_dict = None

        # 构建完整路径
        minite_path = os.path.join(root_dir, minite_dir)
        thickness_path = os.path.join(root_dir, thickness_dir)
        data_path = os.path.join(root_dir, data_file)

        # 验证路径存在
        if not os.path.isdir(minite_path):
            raise FileNotFoundError(f"minite_points目录不存在: {minite_path}")
        if not os.path.isdir(thickness_path):
            raise FileNotFoundError(f"other_thickness目录不存在: {thickness_path}")
        if not os.path.isfile(data_path):
            raise FileNotFoundError(f"刚度数据文件不存在: {data_path}")

        # 加载刚度数据字典
        stiffness_data = np.load(data_path, allow_pickle=True).item()
        print('原始刚度数据:', stiffness_data)

        # 创建从文件路径到刚度值的映射字典
        self.stiffness_dict = {}
        # 假设每个文件路径对应一个刚度值
        for path, stiffness_value in zip(stiffness_data['columns'], stiffness_data['data']):
            # 从路径中提取run_id
            match = re.search(r'run[-_](\d{5})', path)
            if match:
                run_id = match.group(1)
                stiffness_key = f"run_{run_id}"
                self.stiffness_dict[stiffness_key] = stiffness_value
        print('重构后的刚度字典:', self.stiffness_dict)

        # 收集minite_points文件夹中的所有图像文件
        minite_files = [f for f in os.listdir(minite_path)   # minite_points文件夹中路径下的文件列表。
                       if f.endswith('.png') and "components_multi" in f]
        # minite_files:['R-001_00001_components_multi.png', 'R-001_00002_components_multi.png']

        # 为每个minite文件匹配对应的thickness文件和刚度值
        for minite_file in minite_files:
            # 提取run编号 (例如从"R-001_00001_components_multi.png"中提取"00001")
            match = re.search(r'[-_](\d{5})[-_]components_multi\.png$', minite_file)
            if not match:
                continue

            run_id = match.group(1)  # 例如 "00001"
            stiffness_key = f"run_{run_id}"   # 例如 "run_00001" 因为result,csv中名字

            # 检查刚度数据中是否存在对应的run
            if stiffness_key not in self.stiffness_dict:
                print(f"警告: 刚度数据中没有找到 {stiffness_key}")
                continue

            # 构建thickness文件名 (与minite文件同名)
            thickness_file = minite_file

            minite_full = os.path.join(minite_path, minite_file)
            thickness_full = os.path.join(thickness_path, thickness_file)

            # 检查thickness文件是否存在
            if not os.path.isfile(thickness_full):
                continue

            # 存储样本信息:将同一车门的厚度灰度图，中间板灰度图以及对应刚度数据放到一个字典中
            self.samples.append({
                'minite': minite_full,
                'thickness': thickness_full,
                'stiffness': self.stiffness_dict[stiffness_key],
                'run_id': run_id
            })

        if not self.samples:
            raise RuntimeError(f"未找到有效的样本: {root_dir}")

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]

        # 加载minite_points图像 (通道1)
        minite_img = Image.open(sample['minite'])

        # 加载other_thickness图像 (通道2)
        thickness_img = Image.open(sample['thickness'])

        # 创建双通道图像,本代码使用先将灰度图转为数组再转为灰度图的方法，还有Transform.ToTensor的方式
        minite_array = np.array(minite_img)
        thickness_array = np.array(thickness_img)

        # 确保两个图像尺寸相同，后续需要更改，因为比如R-01与R-06车门的图片大小不一致。
        if minite_array.shape != thickness_array.shape:
            raise RuntimeError(
                f"图像尺寸不匹配: {sample['minite']} ({minite_array.shape}) "
                f"vs {sample['thickness']} ({thickness_array.shape})"
            )

        # 合并为双通道图像 [H, W, 2]
        dual_channel = np.stack([minite_array, thickness_array], axis=-1)

        # 转换为PyTorch张量并归一化
        dual_channel = dual_channel.astype(np.float32) / 255.0

        # 转换为CHW格式 [2, H, W] 神经网络中的输入通常要求数据的格式是 [C, H, W] H：表示图像的高度（像素的行数）
        dual_channel = np.transpose(dual_channel, (2, 0, 1))

        # 应用变换（如果有）
        if self.transform:
            dual_channel = self.transform(dual_channel)

        # 获取刚度值
        stiffness = sample['stiffness']

        return (
            torch.tensor(dual_channel, dtype=torch.float32),
            torch.tensor([stiffness], dtype=torch.float32),
            sample['run_id'],  # 返回run_id用于调试
            # sample['minite'],  # 新增：minite图像路径
            # sample['thickness']  # 新增：thickness图像路径
        )

# # 使用示例
# if __name__ == "__main__":
#     # 设置数据集路径
#     dataset_path = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\R-001\pipei"
#
#     # 创建数据集实例
#     dataset = CarDoorStiffnessDataset(dataset_path)
#
#     print(f"数据集大小: {len(dataset)} 个样本")
#
#     # 获取第一个样本
#     input_tensor, stiffness_tensor, run_id = dataset[0]
#
#     print(f"输入张量形状: {input_tensor.shape}")  # 应为 [2, H, W]
#     print(f"刚度值: {stiffness_tensor.item()}")
#     print(f"Run ID: {run_id}")
#
#     # 可视化第一个通道（minite_points）
#     import matplotlib.pyplot as plt
#
#     plt.figure(figsize=(12, 6))
#
#     plt.subplot(1, 2, 1)
#     plt.imshow(input_tensor[0].numpy(), cmap='gray')
#     plt.title(f"minite_points (Run {run_id})")
#
#     plt.subplot(1, 2, 2)
#     plt.imshow(input_tensor[1].numpy(), cmap='gray')
#     plt.title(f"other_thickness (Run {run_id})")
#
#     plt.tight_layout()
#     plt.show()