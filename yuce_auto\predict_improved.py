#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的车门刚度预测验证脚本
================================

主要改进：
1. 更详细的错误分析
2. 按车型分组分析
3. 改进的可视化
4. 异常值检测和分析

作者：AI Assistant
创建时间：2025-01-25
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from dataset_tongdao_auto import CarDoorStiffnessDataset
import os
import re

# 导入模型
from model import ResNet_18_2D
from model34_new import ResNet_34_2D
from model50_new import ResNet_50_2D

# 配置参数
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
VAL_PATH = r"D:\Aprojecdtdoor\Dataset_Construction\code-v2\sample\CX\CX_lower_upper_wf\val"
BATCH_SIZE = 1  # 验证时使用单样本
SMAPE_EPS = 1e-6
ATTENTION_FACTOR = 0.5

def smape(pred, true, eps=SMAPE_EPS):
    """计算对称平均绝对百分比误差"""
    pred = pred.view(-1)
    true = true.view(-1)
    return 1 - torch.abs(pred - true) / (torch.abs(pred) + torch.abs(true) + eps)

def analyze_by_model_type(results_df):
    """按车型分析预测结果"""
    print("\n=== 按车型分析 ===")
    
    # 提取车型信息
    results_df['model_type'] = results_df['model_id'].str.extract(r'([A-Za-z]-\d+)')
    
    model_stats = []
    for model_type in results_df['model_type'].unique():
        if pd.isna(model_type):
            continue
            
        model_data = results_df[results_df['model_type'] == model_type]
        
        stats = {
            'model_type': model_type,
            'sample_count': len(model_data),
            'mse': np.mean((model_data['predicted'] - model_data['true']) ** 2),
            'mae': np.mean(np.abs(model_data['predicted'] - model_data['true'])),
            'r2': 1 - np.sum((model_data['true'] - model_data['predicted']) ** 2) / 
                     np.sum((model_data['true'] - np.mean(model_data['true'])) ** 2),
            'avg_smape': np.mean(model_data['smape']),
            'within_15_percent': np.sum(np.abs(model_data['relative_error']) <= 0.15) / len(model_data),
            'max_error': np.max(np.abs(model_data['relative_error'])),
            'avg_true_stiffness': np.mean(model_data['true']),
            'std_true_stiffness': np.std(model_data['true'])
        }
        model_stats.append(stats)
    
    model_stats_df = pd.DataFrame(model_stats)
    model_stats_df = model_stats_df.sort_values('mse')
    
    print(model_stats_df.to_string(index=False, float_format='%.4f'))
    
    return model_stats_df

def detect_outliers(results_df, threshold=0.3):
    """检测异常值"""
    print(f"\n=== 异常值分析 (相对误差 > {threshold*100}%) ===")
    
    outliers = results_df[np.abs(results_df['relative_error']) > threshold]
    
    if len(outliers) > 0:
        print(f"发现 {len(outliers)} 个异常样本:")
        outlier_summary = outliers[['img_file', 'model_id', 'true', 'predicted', 'relative_error']].copy()
        outlier_summary['abs_error'] = np.abs(outlier_summary['relative_error'])
        outlier_summary = outlier_summary.sort_values('abs_error', ascending=False)
        
        print(outlier_summary.head(10).to_string(index=False, float_format='%.4f'))
        
        # 分析异常值的车型分布
        print(f"\n异常值车型分布:")
        outlier_model_dist = outliers['model_id'].str.extract(r'([A-Za-z]-\d+)')[0].value_counts()
        print(outlier_model_dist)
        
        return outliers
    else:
        print("未发现异常值")
        return pd.DataFrame()

def create_improved_visualization(results_df, outliers_df, save_path="validation_improved.png"):
    """创建改进的可视化图表"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 改进的散点图
    ax1 = axes[0, 0]
    
    # 正常点
    normal_data = results_df[~results_df.index.isin(outliers_df.index)]
    ax1.scatter(normal_data['true'], normal_data['predicted'], 
               alpha=0.6, c='green', s=30, label='Normal')
    
    # 异常点
    if len(outliers_df) > 0:
        ax1.scatter(outliers_df['true'], outliers_df['predicted'], 
                   alpha=0.8, c='red', s=50, label='Outliers')
    
    # 理想预测线和误差范围
    min_val, max_val = results_df['true'].min(), results_df['true'].max()
    ax1.plot([min_val, max_val], [min_val, max_val], 'b--', label='Perfect Prediction')
    ax1.fill_between([min_val, max_val], 
                     [min_val*0.85, max_val*0.85], 
                     [min_val*1.15, max_val*1.15], 
                     alpha=0.2, color='red', label='±15% Error Range')
    
    ax1.set_xlabel('True Stiffness')
    ax1.set_ylabel('Predicted Stiffness')
    ax1.set_title('True vs Predicted Stiffness (Improved)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 相对误差分布
    ax2 = axes[0, 1]
    ax2.hist(results_df['relative_error'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(0, color='red', linestyle='--', label='Perfect Prediction')
    ax2.axvline(0.15, color='orange', linestyle='--', label='15% Error')
    ax2.axvline(-0.15, color='orange', linestyle='--')
    ax2.set_xlabel('Relative Error')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Relative Error Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 按车型的性能对比
    ax3 = axes[1, 0]
    model_types = results_df['model_id'].str.extract(r'([A-Za-z]-\d+)')[0]
    model_performance = []
    model_labels = []
    
    for model_type in model_types.unique():
        if pd.isna(model_type):
            continue
        model_data = results_df[model_types == model_type]
        model_performance.append(np.abs(model_data['relative_error']))
        model_labels.append(f"{model_type}\n(n={len(model_data)})")
    
    if model_performance:
        ax3.boxplot(model_performance, labels=model_labels)
        ax3.set_ylabel('Absolute Relative Error')
        ax3.set_title('Prediction Error by Model Type')
        ax3.grid(True, alpha=0.3)
        plt.setp(ax3.get_xticklabels(), rotation=45)
    
    # 4. 真实刚度 vs 预测误差
    ax4 = axes[1, 1]
    scatter = ax4.scatter(results_df['true'], np.abs(results_df['relative_error']), 
                         c=results_df['smape'], cmap='viridis', alpha=0.6)
    ax4.set_xlabel('True Stiffness')
    ax4.set_ylabel('Absolute Relative Error')
    ax4.set_title('Error vs True Stiffness (colored by SMAPE)')
    ax4.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax4, label='SMAPE')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"改进的可视化图表已保存为: {save_path}")

def validate_improved():
    """改进的验证函数"""
    print("=== 改进的车门刚度预测验证 ===")
    
    # 1. 初始化数据集
    val_dataset = CarDoorStiffnessDataset(
        root_dir=VAL_PATH,
        target_size=(270, 270),
        padding_mode='constant'
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    print(f"验证集大小: {len(val_dataset)} 样本")
    print(f"通道数量: {len(val_dataset.channel_dirs)}")
    
    # 2. 初始化模型
    num_channels = len(val_dataset.channel_dirs)
    model = ResNet_34_2D(
        in_channels=num_channels,
        attention_factor=ATTENTION_FACTOR
    ).to(DEVICE)
    
    # 3. 加载训练好的模型权重
    try:
        model.load_state_dict(torch.load("model_final.pth", map_location=DEVICE))
        print("✅ 模型加载成功")
    except FileNotFoundError:
        print("❌ 未找到模型文件 model_final.pth")
        return
    
    model.eval()
    
    # 4. 进行预测
    predictions = []
    true_values = []
    smape_values = []
    img_files = []
    model_ids = []
    
    print("开始预测...")
    with torch.no_grad():
        for batch_idx, (data, target, img_file, model_id, run_id) in enumerate(val_loader):
            data, target = data.to(DEVICE), target.to(DEVICE)
            
            output = model(data)
            pred_value = output.cpu().numpy().flatten()[0]
            true_value = target.cpu().numpy().flatten()[0]
            
            # 计算SMAPE
            smape_val = smape(output, target).cpu().numpy().flatten()[0]
            
            predictions.append(pred_value)
            true_values.append(true_value)
            smape_values.append(smape_val)
            img_files.append(img_file[0])
            model_ids.append(model_id[0])
            
            if (batch_idx + 1) % 50 == 0:
                print(f"已处理 {batch_idx + 1}/{len(val_loader)} 样本")
    
    # 5. 创建结果DataFrame
    results_df = pd.DataFrame({
        'img_file': img_files,
        'model_id': model_ids,
        'true': true_values,
        'predicted': predictions,
        'smape': smape_values
    })
    
    # 计算相对误差
    results_df['relative_error'] = (results_df['predicted'] - results_df['true']) / results_df['true']
    results_df['abs_relative_error'] = np.abs(results_df['relative_error'])
    
    # 6. 计算整体评估指标
    preds = np.array(predictions)
    trues = np.array(true_values)
    
    mse = np.mean((preds - trues) ** 2)
    mae = np.mean(np.abs(preds - trues))
    smape_val = 1 - np.mean(np.abs(preds - trues) / (np.abs(preds) + np.abs(trues) + SMAPE_EPS))
    avg_smape = np.mean(smape_values)
    r_squared = 1 - np.sum((trues - preds) ** 2) / np.sum((trues - np.mean(trues)) ** 2)
    
    within_15_percent = np.sum(np.abs(results_df['relative_error']) <= 0.15) / len(results_df)
    within_10_percent = np.sum(np.abs(results_df['relative_error']) <= 0.10) / len(results_df)
    within_5_percent = np.sum(np.abs(results_df['relative_error']) <= 0.05) / len(results_df)
    
    print(f"\n=== 整体性能指标 ===")
    print(f"样本数量: {len(results_df)}")
    print(f"MSE: {mse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"R²: {r_squared:.4f}")
    print(f"SMAPE: {smape_val:.4f}")
    print(f"平均SMAPE: {avg_smape:.4f}")
    print(f"5%误差范围内: {within_5_percent:.1%}")
    print(f"10%误差范围内: {within_10_percent:.1%}")
    print(f"15%误差范围内: {within_15_percent:.1%}")
    print(f"最大相对误差: {np.max(np.abs(results_df['relative_error'])):.1%}")
    print(f"平均相对误差: {np.mean(np.abs(results_df['relative_error'])):.1%}")
    
    # 7. 按车型分析
    model_stats_df = analyze_by_model_type(results_df)
    
    # 8. 异常值检测
    outliers_df = detect_outliers(results_df, threshold=0.2)
    
    # 9. 保存详细结果
    results_df.to_csv('validation_results_improved.csv', index=False)
    print(f"\n详细结果已保存为: validation_results_improved.csv")
    
    # 10. 创建改进的可视化
    create_improved_visualization(results_df, outliers_df)
    
    return results_df, model_stats_df, outliers_df

if __name__ == "__main__":
    validate_improved()
