import openpyxl
import os
import re


def modify_excel_paths(file_path):
    """
    修改Excel表格中指定行的文件路径名称
    参数:
        file_path: Excel文件路径
    """
    # 加载工作簿
    wb = openpyxl.load_workbook(file_path)
    ws = wb.active

    # 需要修改的行号
    target_rows = [1, 10, 15, 18, 25]

    # 遍历所有目标行
    for row_idx in target_rows:
        # 获取该行中的所有单元格
        row_cells = list(ws[row_idx])

        # 遍历行中的每个单元格
        for cell in row_cells:
            if cell.value and isinstance(cell.value, str):
                # 匹配Windows路径格式
                if "\\" in cell.value:
                    # 使用正则表达式匹配路径格式
                    match = re.search(r"(.*\\collector\\.*)", cell.value)
                    if match:
                        # 提取collector之后的部分
                        path_after_collector = match.group(1).split("\\collector\\")[1]
                        # 构建新的相对路径
                        new_value = "..\\collector\\" + path_after_collector
                        cell.value = new_value

                # 匹配Unix路径格式
                elif "/" in cell.value:
                    # 使用正则表达式匹配路径格式
                    match = re.search(r"(.*/collector/.*)", cell.value)
                    if match:
                        # 提取collector之后的部分
                        path_after_collector = match.group(1).split("/collector/")[1]
                        # 构建新的相对路径
                        new_value = "../collector/" + path_after_collector
                        cell.value = new_value

    # 保存修改后的工作簿
    new_file_path = os.path.splitext(file_path)[0] + "_modified.xlsx"
    wb.save(new_file_path)
    print(f"文件已保存为: {new_file_path}")


if __name__ == "__main__":
    excel_file = "R_011result.xlsx"  # 替换为你的Excel文件路径
    modify_excel_paths(excel_file)