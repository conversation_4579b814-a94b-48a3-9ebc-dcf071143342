#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行批量实验启动器 - Command Line Batch Experiment Runner
========================================================

专为GPU服务器环境设计的无GUI版本
使用方法：
python run_experiments_cli.py <训练数据路径> <验证数据路径>

示例：
python run_experiments_cli.py /path/to/train /path/to/val
"""

import sys
import os

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 批量实验管理器 - 命令行版本")
    print("=" * 60)
    
    # 检查参数
    if len(sys.argv) < 3:
        print("使用方法:")
        print("python run_experiments_cli.py <训练数据路径> <验证数据路径>")
        print("\n示例:")
        print("python run_experiments_cli.py /home/<USER>/train /home/<USER>/val")
        print("python run_experiments_cli.py ./train ./val")
        
        # 交互式输入
        print("\n或者现在输入路径:")
        train_path = input("训练数据路径: ").strip()
        val_path = input("验证数据路径: ").strip()
        
        if not train_path or not val_path:
            print("❌ 请提供有效的数据路径")
            sys.exit(1)
    else:
        train_path = sys.argv[1]
        val_path = sys.argv[2]
    
    # 验证路径
    if not os.path.exists(train_path):
        print(f"❌ 训练数据路径不存在: {train_path}")
        sys.exit(1)
        
    if not os.path.exists(val_path):
        print(f"❌ 验证数据路径不存在: {val_path}")
        sys.exit(1)
    
    print(f"✅ 训练数据路径: {train_path}")
    print(f"✅ 验证数据路径: {val_path}")
    
    # 导入并运行命令行管理器
    try:
        from batch_experiment_manager import CommandLineBatchManager
        
        print("🔧 初始化实验管理器...")
        manager = CommandLineBatchManager()
        
        print("🚀 开始批量实验...")
        manager.run_all_experiments(train_path, val_path)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 batch_experiment_manager.py 文件存在")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断实验")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
