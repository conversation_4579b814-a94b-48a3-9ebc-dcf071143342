import numpy as np
from PIL import Image
import matplotlib
import matplotlib.pyplot as plt
import os
import math
from scipy import ndimage
"""
该代码将体素化中新更改的体素通道转为灰度图，为搭建外腰线工况下的预测模型，灰度图中增加腰线加强板通道，此外将厚度通道进行拆解
实现自动处理load id 在断面提取时直接从NPZ文件中读取加载点坐标
"""

# 解决中文显示问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def extract_cross_section(voxel_grid, loading_point, section_plane='yz',
                          channel_idx=11, rotate_angle=0, thickness=3,
                          thickness_method='max'):
    """
    在指定平面上提取通过加载点的断面带

    参数:
        voxel_grid: 体素网格数据 (nx, ny, nz, channels)
        loading_point: 加载点体素索引 (i, j, k)
        section_plane: 断面平面 ('xy', 'xz', 'yz')
        channel_idx: 要提取的通道索引
        rotate_angle: 旋转角度
        thickness: 断面带厚度 (层数)
        thickness_method: 多层聚合方法 ('max' 或 'mean')
    返回:
        断面图像 (2D numpy数组)
    """
    # 确保加载点在有效范围内
    i, j, k = loading_point
    nx, ny, nz, _ = voxel_grid.shape
    i = max(0, min(i, nx - 1))
    j = max(0, min(j, ny - 1))
    k = max(0, min(k, nz - 1))

    # 计算断面带的范围
    half_thickness = thickness // 2

    # 根据指定平面提取多层断面
    section_stack = []

    if section_plane.lower() == 'xy':
        # XY平面 (固定Z)
        z_start = max(0, k - half_thickness)
        z_end = min(nz, k + half_thickness + 1)

        for z in range(z_start, z_end):
            section = voxel_grid[:, :, z, channel_idx]
            section_stack.append(section)

    elif section_plane.lower() == 'xz':
        # XZ平面 (固定Y)
        y_start = max(0, j - half_thickness)
        y_end = min(ny, j + half_thickness + 1)

        for y in range(y_start, y_end):
            section = voxel_grid[:, y, :, channel_idx]
            section_stack.append(section)

    elif section_plane.lower() == 'yz':
        # YZ平面 (固定X)
        x_start = max(0, i - half_thickness)
        x_end = min(nx, i + half_thickness + 1)

        for x in range(x_start, x_end):
            section = voxel_grid[x, :, :, channel_idx]
            section_stack.append(section)

    else:
        raise ValueError(f"不支持的断面平面: {section_plane}")

    # 将多层断面堆叠为3D数组
    section_stack = np.stack(section_stack)

    # 聚合多层断面
    if thickness_method == 'max':
        aggregated_section = np.max(section_stack, axis=0)
    elif thickness_method == 'mean':
        aggregated_section = np.mean(section_stack, axis=0)
    else:
        raise ValueError(f"不支持的聚合方法: {thickness_method}")

    # 将数据转换为0-255范围
    section_img = np.clip(aggregated_section * 255, 0, 255).astype(np.uint8)

    # 旋转处理
    if rotate_angle != 0:
        section_img = ndimage.rotate(
            section_img,
            rotate_angle,
            order=1,  # 双线性插值
            reshape=True
        )

    return section_img

def npz_to_grayscale(npz_path, output_image_path,
                     projection_axis='z',
                     channel_configs=None,  # 新增：通道配置列表，多通道融合
                     global_projection=None,  # 全局投影方法（可选）
                     normalize=True,
                     vmin=None,
                     vmax=None,
                     flip_axes=False,
                     rotate_angle=0,
                     crop_area=None,
                     show_image=True,
                     resolution=1.0):
    """
    将体素化的NPZ文件转换为2D灰度图

    参数:
        npz_path: NPZ文件路径
        output_image_path: 输出图像路径
        channel_configs: [{
            'channel': 0,                   # 通道索引(0-5)
            'projection': 'max',            # 该通道的投影方法
            'weight': 1.0,                  # 该通道权重
            'projection_weight': 1.0        # 该投影方法权重
        }, ...]
        global_projection: 当channel_configs未指定时使用的全局投影方法
        normalize: 是否归一化到0-255
        vmin: 归一化最小值 (可选)
        vmax: 归一化最大值 (可选)
        flip_axes: 是否翻转图像的X/Y轴
        rotate_angle: 旋转角度 (度)
        crop_area: 裁剪区域 ((x_min, x_max), (y_min, y_max), (z_min, z_max))
        show_image: 是否显示生成的图像
        resolution: 体素分辨率 (mm/体素)
    """
    # 加载NPZ文件
    data = np.load(npz_path)
    voxel_grid = data['voxel']  # 形状: (nx, ny, nz, 12)

    # 裁剪区域处理-车门模型不进行裁剪区域处理
    if crop_area:
        x_range, y_range, z_range = crop_area
        voxel_grid = voxel_grid[x_range[0]:x_range[1],
                     y_range[0]:y_range[1],
                     z_range[0]:z_range[1], :]

    # 通道配置默认值-调用函数时一般已经定义参数
    if channel_configs is None:
        channel_configs = [{
            'channel': 0,
            'projection': global_projection or 'max',
            'weight': 1.0,
            'projection_weight': 1.0
        }]

    # 根据投影方向选择投影轴
    axis_map = {'x': 0, 'y': 1, 'z': 2}
    axis = axis_map.get(projection_axis.lower(), 2)  # projection_axis投影方向 ('x', 'y', 'z')，自己选择参数

    # 多通道投影融合
    projections = []
    # 更新通道名称以匹配12通道格式，新增腰线加强板通道
    channel_names = [
        "外板", "内板", "中间板",
        "胶", "焊点", "RBE2",
        "外板厚度", "内板厚度", "其他板厚度",
        "内外板距离差值",
        "腰线加强板A", "腰线加强板B"
    ]
    used_channels = []

    for config in channel_configs: # 调用channel_configs字典中通道，投影方法，权重
        ch = config['channel']
        method = config['projection']
        ch_weight = config['weight']
        proj_weight = config['projection_weight']

        # 执行投影
        if method == 'max':
            proj = np.max(voxel_grid[..., ch], axis=axis)
        elif method == 'mean':
            proj = np.mean(voxel_grid[..., ch], axis=axis)
        elif method == 'min':
            proj = np.min(voxel_grid[..., ch], axis=axis)
        elif method == 'sum':
            proj = np.sum(voxel_grid[..., ch], axis=axis)
        else:
            raise ValueError(f"不支持的投影方法: {method}")

        # 加权处理
        weighted_proj = proj * ch_weight * proj_weight
        projections.append(weighted_proj)
        used_channels.append(f"{channel_names[ch]}({method})")

    # 合并所有通道投影
    if len(projections) > 1:
        projection = np.sum(projections, axis=0)
    else:
        projection = projections[0]
    # 后处理——翻转
    if flip_axes:
        projection = projection.T

    if rotate_angle != 0:
        projection = ndimage.rotate(projection, rotate_angle, reshape=True)

    # # 归一化
    # if normalize:
    #     vmin = np.min(projection) if vmin is None else vmin
    #     vmax = np.max(projection) if vmax is None else vmax
    #     normalized = ((projection - vmin) / (vmax - vmin) * 255).astype(np.uint8) if (vmax - vmin) != 0 else np.zeros_like(projection)
    # else:
    #     normalized = projection.astype(np.uint8)
    # 🔑 改进归一化逻辑-对厚度单独处理
    if normalize:
        # 判断是否厚度通道单独输出
        is_thickness = (
            len(channel_configs) == 1 and channel_configs[0]['channel'] == 6
        )

        if is_thickness:
            flat = projection.flatten()
            p1 = np.percentile(flat, 1)
            p99 = np.percentile(flat, 99)
            actual_min = p1
            actual_max = p99
            print(f"[厚度归一化] 1%: {p1:.3f}, 99%: {p99:.3f}")
        else:
            actual_min = np.min(projection) if vmin is None else vmin
            actual_max = np.max(projection) if vmax is None else vmax

        if actual_max > actual_min:
            normed = (projection - actual_min) / (actual_max - actual_min)
        else:
            normed = np.zeros_like(projection)

        normalized = (normed * 255).clip(0, 255).astype(np.uint8)
    else:
        normalized = projection.astype(np.uint8)
    # 如果是距离差值通道，做单独的归一化显示
    if 9 in [cfg['channel'] for cfg in channel_configs]:
        distance_channel = np.max(voxel_grid[..., 7], axis=axis)
        flat = distance_channel.flatten()
        p1 = np.percentile(flat, 1)
        p99 = np.percentile(flat, 99)
        actual_min = p1
        actual_max = p99
        print(f"[距离差值归一化] 1%: {p1:.3f}, 99%: {p99:.3f}")
        normed_distance = (distance_channel - actual_min) / (actual_max - actual_min)
        normalized_distance = (normed_distance * 255).clip(0, 255).astype(np.uint8)
        Image.fromarray(normalized_distance).save(output_image_path.replace('.png', '_distance.png'))
        print(f"已保存内外板距离差值图像: {output_image_path.replace('.png', '_distance.png')}")


    # 保存图像
    Image.fromarray(normalized).save(output_image_path)
    print(f"已保存: {output_image_path}")

    # 显示结果
    if show_image:
        plt.figure(figsize=(12, 8))
        plt.imshow(normalized, cmap='gray', vmin=0, vmax=255)
        plt.title(f"投影方向: {projection_axis.upper()}\n融合通道: {' + '.join(used_channels)}")
        plt.colorbar(label='灰度强度')
        plt.axis('off')
        plt.show()

    return normalized


def batch_process_npz(input_dir, output_dir, **kwargs):
    """批量处理增强版"""
    os.makedirs(output_dir, exist_ok=True)

    for file in os.listdir(input_dir):
        if file.lower().endswith(".npz"):
            npz_path = os.path.join(input_dir, file)
            output_path = os.path.join(output_dir, f"{os.path.splitext(file)[0]}.png")

            try:
                npz_to_grayscale(npz_path, output_path, **kwargs)
            except Exception as e:
                print(f"处理 {file} 失败: {str(e)}")

# 使用示例
if __name__ == "__main__":
    # 示例NPZ文件路径 - 替换为实际路径
    input_dir = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\all_sample_hdt_result\try"

    # 输出目录 - 确保存在
    output_dir= r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\all_sample_hdt_result\try"
    os.makedirs(output_dir, exist_ok=True)

    # 示例1：复杂多通道融合（包含新通道）
    fusion_dir = os.path.join(output_dir, "all_channels_fusion")
    os.makedirs(fusion_dir, exist_ok=True)
    all_channel_config = [
        # 通道0：外板 —— 用 max 突出边界，权重 1.0（基础轮廓）
        {'channel': 0, 'projection': 'max', 'weight': 1.0, 'projection_weight': 1.0},
        # 通道1：1: 内板
        {'channel': 1, 'projection': 'max', 'weight': 1, 'projection_weight': 1.0},
        # 通道2：2: 中间板
        {'channel': 2, 'projection': 'mean', 'weight': 1, 'projection_weight': 1.0},
        # 通道3：胶 —— 用 max 显示胶域，权重 0.8（辅助粘结区域）
        {'channel': 3, 'projection': 'max', 'weight': 0.8, 'projection_weight': 1.0},
        # 通道4：焊点 —— 用 sum 累积焊点数量，权重 1.5（焊点聚集区重点）
        {'channel': 4, 'projection': 'sum', 'weight': 1.5, 'projection_weight': 1.0},
        # 通道5：RBE2 —— 用 max 标记 RBE2 连接点，权重 0.7（连接关节）
        {'channel': 5, 'projection': 'max', 'weight': 0.7, 'projection_weight': 1.0},
        # 通道6：外板厚度
        {'channel': 6, 'projection': 'sum', 'weight': 1.2, 'projection_weight': 1.0},
        # 通道7：内板厚度
        {'channel': 7, 'projection': 'sum', 'weight': 1.2, 'projection_weight': 1.0},
        # 通道8：其他板厚度
        {'channel': 8, 'projection': 'sum', 'weight': 1.2, 'projection_weight': 1.0},
        # 通道9：内外板距离差值
        {'channel': 9, 'projection': 'max', 'weight': 1.0, 'projection_weight': 1.0},
        # 通道10：腰线加强板A
        {'channel': 10, 'projection': 'max', 'weight': 1.0, 'projection_weight': 1.0},
        # 通道11：腰线加强板B
        {'channel': 11, 'projection': 'max', 'weight': 1.0, 'projection_weight': 1.0},
    ]

    batch_process_npz(
        input_dir,
        fusion_dir,
        projection_axis='y',  # 或 'x'/'y' 根据需求
        channel_configs=all_channel_config,
        flip_axes=False,
        rotate_angle=90,
        show_image=False
    )


    # 示例2：通道单一输出 0: 外板
    entity_only_dir = os.path.join(output_dir, "outer_points")
    os.makedirs(entity_only_dir, exist_ok=True)

    batch_process_npz(
        input_dir,
        entity_only_dir,
        projection_axis='y',
        channel_configs=[{
            'channel': 0,
            'projection': 'max',
            'weight': 1.0,
            'projection_weight': 1.0
        }],
        flip_axes=False,
        rotate_angle=90,
        show_image=False
    )
    # -------- 单一通道输出示例 --------
    # 通道说明：
    #  0 外板, 1: 内板, 2: 中间板, 3: 胶, 4: 焊点, 5: RBE2
    #  6: 外板厚度, 7: 内板厚度, 8: 其他板厚度, 9: 内外板距离差值
    #  10: 腰线加强板A, 11: 腰线加强板B

    single_channel_dirs = {
        1: "inner_points",  # 内板
        2: "minite_points",  # 中间板
        3: "glue_only",  # 胶
        4: "weld_only",  # 焊点
        5: "rbe2_only",  # RBE2
        6: "outer_thickness",  # 外板厚度
        7: "inner_thickness",  # 内板厚度
        8: "other_thickness",  # 其他板厚度
        9: "distance",  # 内外板距离差值
        10: "reinforce_A",  # 腰线加强板A
        11: "reinforce_B",  # 腰线加强板B
    }

    # 为每个通道生成单独的投影图（使用 max 投影示例）
    for ch, subfolder in single_channel_dirs.items():
        save_dir = os.path.join(output_dir, subfolder)
        os.makedirs(save_dir, exist_ok=True)

        # 配置：仅当前通道，max 投影，权重均为 1
        ch_config = [{
            'channel': ch,
            'projection': 'max',
            'weight': 1.0,
            'projection_weight': 1.0
        }]

        print(f"\n=== 生成通道 {ch} ({subfolder}) 投影 ===")
        batch_process_npz(
            input_dir,
            save_dir,
            projection_axis='y',  # 也可以改成 'x' 或 'y'
            channel_configs=ch_config,
            flip_axes=False,
            rotate_angle=90,
            show_image=False
        )

    # 示例3：厚度通道组合
    thickness_dir = os.path.join(output_dir, "thickness_combination")
    os.makedirs(thickness_dir, exist_ok=True)

    thickness_config = [
        {'channel': 6, 'projection': 'sum', 'weight': 1.0, 'projection_weight': 1.0},  # 外板厚度
        {'channel': 7, 'projection': 'sum', 'weight': 1.0, 'projection_weight': 1.0},  # 内板厚度
        {'channel': 8, 'projection': 'sum', 'weight': 1.0, 'projection_weight': 1.0},  # 其他板厚度
    ]

    batch_process_npz(
        input_dir,
        thickness_dir,
        projection_axis='y',
        channel_configs=thickness_config,
        flip_axes=False,
        rotate_angle=90,
        show_image=False
    )
    # ========================================================
    # 单独处理断面图像（使用第12通道）
    # ========================================================

    section_dir = os.path.join(output_dir, "cross_sections")
    os.makedirs(section_dir, exist_ok=True)

    # 为每个NPZ文件生成断面图像
    for file in os.listdir(input_dir):
        if file.lower().endswith(".npz"):
            npz_path = os.path.join(input_dir, file)
            # 加载数据
            data = np.load(npz_path)
            voxel_grid = data['voxel']

            # 使用手动指定的加载点-修改提取npz文件中加载点
            # loading_point = MANUAL_LOADING_POINT
            if 'loading_point' in data:
                loading_point = tuple(data['loading_point'])
                print(f"从 {file} 中获取加载点: {loading_point}")
            else:
                print(f"⚠ 警告: {file} 中没有加载点信息，跳过")
                continue
            # 提取断面,调用extract_cross_section函数
            section_img = extract_cross_section(
                voxel_grid,
                loading_point,
                section_plane='yz',
                channel_idx=12,
                rotate_angle=90,  # 向左旋转90°
                thickness=5,  # 取5层形成断面带
                thickness_method='max'  # 取最大值聚合
            )

            # 保存断面图像
            output_path = os.path.join(section_dir, f"{os.path.splitext(file)[0]}.png")
            Image.fromarray(section_img).save(output_path)
            print(f"已保存断面图像: {output_path}")