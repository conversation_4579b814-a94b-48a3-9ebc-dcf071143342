import os
import numpy as np
import torch
from torch.utils.data import Dataset
from PIL import Image
import re


class CarDoorStiffnessDataset(Dataset):
    def __init__(self, root_dir, data_file="extracted_data.npy",
                 ignore_files=["extracted_data.npy", "extracted_data.txt"],
                 transform=None, target_size=(270, 270), padding_mode='constant'): # 新增target_size参数
        """
        车门刚度数据集加载器（改进版），实现等比例缩放+填充
        参数:
            root_dir: 根目录路径（包含多个通道文件夹和刚度数据文件）
            data_file: 包含刚度值的npy文件名
            ignore_files: 需要忽略的文件列表
            transform: 可选的数据增强/预处理变换
            target_size: 统一调整图片的目标尺寸 (height, width) # 新增
            padding_mode: 填充模式 ('constant' 或 'reflect')
        """
        self.root_dir = root_dir
        self.transform = transform
        self.samples_dict = {}  # 存储样本信息: {图片文件名: {通道名: 路径, "stiffness": 值}}
        self.channel_dirs = []  # 存储通道文件夹路径
        self.ignore_files = ignore_files
        self.target_size = target_size  # 新增保存目标尺寸
        self.padding_mode = padding_mode  # 填充模式
        # 验证根目录存在
        if not os.path.isdir(root_dir):
            raise FileNotFoundError(f"根目录不存在: {root_dir}")
        
        # 1.扫描根目录下的所有文件夹（作为通道）-最后生成文件夹的名字，如['cross_sections', 'inner_points']
        self._scan_channel_dirs()

        # 2.加载刚度数据,最终是车门图片名与刚度值的映射：R_037_00010_components_multi.png -> 111.8
        data_path = os.path.join(root_dir, data_file)
        if not os.path.isfile(data_path):
            raise FileNotFoundError(f"刚度数据文件不存在: {data_path}")

        self._load_stiffness_data(data_path)

        # 3.收集所有样本
        self._collect_samples()

    @staticmethod
    def resize_with_padding(img, target_size, padding_mode='constant'):
        """
        等比例缩放图像并填充到目标尺寸
        参数:
            img: PIL Image 输入图片
            target_size: 目标尺寸 (height, width)
            padding_mode: 填充模式 ('constant' 或 'reflect')
        返回:
            填充后的numpy数组 (H, W)
        """
        target_h, target_w = target_size  # 目标高度，宽度
        orig_w, orig_h = img.size  # PIL 尺寸格式 (width, height)

        # 计算缩放比例，等比例缩放
        ratio = min(target_h / orig_h, target_w / orig_w)   # 先缩放比例最小的。比如img = (228, 260)，先h=260会先完成缩放到270，而new_w=237
        new_h = int(round(orig_h * ratio))
        new_w = int(round(orig_w * ratio))

        # 等比例缩放
        img = img.resize((new_w, new_h), Image.BILINEAR)
        img_array = np.array(img)  # 转换为numpy数组 (H, W)

        # 计算需要填充的边界
        pad_top = (target_h - new_h) // 2
        pad_bottom = target_h - new_h - pad_top
        pad_left = (target_w - new_w) // 2   # 比如高度方向因为已经是270，而宽度上new_w=237，要进行填充，宽度需要270 − 237 = 33 像素的填充；为了居中，就各边各填一半
        pad_right = target_w - new_w - pad_left

        # 应用填充
        if padding_mode == 'reflect':
            padded_img = np.pad(
                img_array,
                ((pad_top, pad_bottom), (pad_left, pad_right)),
                mode='reflect'
            )
        else:  # 默认使用常数填充 (零填充)
            padded_img = np.pad(
                img_array,
                ((pad_top, pad_bottom), (pad_left, pad_right)),
                mode='constant',
                constant_values=0
            )

        return padded_img

    def _scan_channel_dirs(self):
        """扫描根目录下的通道文件夹"""
        for entry in os.listdir(self.root_dir):  # 根目录下所有文件夹名字
            full_path = os.path.join(self.root_dir, entry)  # 进入每个文件夹所在目录
            if os.path.isdir(full_path) and entry not in self.ignore_files:
                self.channel_dirs.append(entry)        # 把通道文件夹名字放入self.channel_dirs
        print(f"找到 {len(self.channel_dirs)} 个通道文件夹: {self.channel_dirs}")

    def _load_stiffness_data(self, data_path):
        """加载刚度数据并创建映射字典"""
        stiffness_data = np.load(data_path, allow_pickle=True).item()  # 加载刚度数据字典，格式见笔记1.车门整体工况建立预测模型
        print(f"加载刚度数据，包含 {len(stiffness_data['img_files'])} 个样本")
        print("stiffness_data", stiffness_data)
        # 创建从图片文件名到刚度值的映射
        self.stiffness_dict = {}
        for img_file, stiffness_value in zip(stiffness_data['img_files'], stiffness_data['data']):
            # 统一文件名格式（去除路径，保留文件名）
            base_name = os.path.basename(img_file)
            self.stiffness_dict[base_name] = stiffness_value
            print(f"刚度映射: {base_name} -> {stiffness_value}")  # 车门图片名与刚度值的映射：R_037_00010_components_multi.png -> 111.8

    def _collect_samples(self):
        """收集所有样本信息"""
        # 1.获取所有通道文件夹中的图片文件，要的是all_files：储存着通道文件夹中图片的名字
        all_files = {}
        for channel in self.channel_dirs:
            channel_path = os.path.join(self.root_dir, channel) # 进入每个通道文件夹中，这里都是灰度图
            files = [f for f in os.listdir(channel_path) if f.endswith('.png')]  # 每个通道中灰度图的名字
            all_files[channel] = set(files)
            print(f"通道 '{channel}' 包含 {len(files)} 张图片")

        # 2.找出所有通道都存在的图片（有效的样本），要的是common_files
        common_files = set.intersection(*[files for files in all_files.values()]) # 求交集，每个通道共有的图片名字
        if not common_files:
            raise RuntimeError("未找到所有通道共有的图片文件")
        print(f"找到 {len(common_files)} 个有效样本")

        # 3.为每个样本构建记录，最终要的是self.samples_dict
        for img_file in common_files:
            # 检查刚度数据中是否有该文件
            if img_file not in self.stiffness_dict:
                print(f"警告: 图片 {img_file} 没有对应的刚度值，跳过")
                continue

            # 初始化样本记录
            sample_record = {}

            # 添加各通道的图片路径
            for channel in self.channel_dirs:
                sample_record[channel] = os.path.join(self.root_dir, channel, img_file)  # 通道中图片的路径

            # 添加刚度值
            sample_record['stiffness'] = self.stiffness_dict[img_file] # stiffness_dict门图片名与刚度值的映射：R_037_00010_components_multi.png -> 111.8

            # 添加到样本字典
            self.samples_dict[img_file] = sample_record  # img_file图片名字  # 字典类型见最下方

        if not self.samples_dict:
            raise RuntimeError("未找到任何有效样本")
        print(f"samples_dict储存输入输出数据格式 {self.samples_dict} ")
        print(f"成功创建 {len(self.samples_dict)} 个样本记录")

    def __len__(self):
        return len(self.samples_dict)

    def __getitem__(self, idx):
        # 1.通过索引获取样本键名，获取对某个车门所有的通道图片，比如R_011_00009车门不同通道
        img_file = list(self.samples_dict.keys())[idx]  #  idx为0:R_011_00009_components_multi.png
        sample = self.samples_dict[img_file]  # 某个车门中对应的不同通道所在的路径，
        # 比如'cross_sections':路径s\\R_011_00009_components_multi.png' 'inner_points':路径\\R_011_00009_components_multi.png'

        # 提取样本ID信息
        match = re.search(r'([A-Za-z])[-_](\d+)[-_](\d{5})[-_]components_multi\.png$', img_file)
        if match:
            model_type = match.group(1)  # 车型类型
            model_num = match.group(2)   # 车型编号
            run_id = match.group(3)      # 样本ID
            model_id = f"{model_type}_{model_num}"  # 完整车型ID
        else:
            print(f"警告: 无法解析文件名 {img_file}")
            model_id = "Unknown"
            run_id = "00000"
        # 2.加载所有通道的图像，最终要channel_images
        channel_images = []  # 


        # 按通道名称排序以确保顺序一致，最终要channel_images
        sorted_channels = sorted([c for c in sample.keys() if c != 'stiffness'])  # 输入通道的名字/通道文件夹的名字
        for channel in sorted_channels:
            img_path = sample[channel]   # 比如获取R_011_00009_components_multi.png的cross_sections通道灰度图路径
            img = Image.open(img_path)  # 打开某个图片
            # 应用等比例缩放+填充 调用函数
            if self.target_size:
                img_array = self.resize_with_padding(img, self.target_size, self.padding_mode)  # img输入的图像，self.target_size目标图像
            else:
                img_array = np.array(img)


            channel_images.append(img_array)  # 在一个idx在，channel_images储存是一个车门的不同通道图片

        # 3.多通道图像转张量，
        # 堆叠为多通道图像 [H, W, C]
        multi_channel = np.stack(channel_images, axis=-1)

        # 转换为PyTorch张量并归一化
        multi_channel = multi_channel.astype(np.float32) / 255.0

        # 转换为CHW格式 [C, H, W]
        multi_channel = np.transpose(multi_channel, (2, 0, 1))   # multi_channel将一个车门的不同通道图片打包一起，实现了图片的匹配

        # 应用变换（如果有）
        if self.transform:
            multi_channel = self.transform(multi_channel)

        return (
            multi_channel,  # 多通道图像张量
            torch.tensor([sample['stiffness']], dtype=torch.float32),  # 刚度值 ，sample是某个车门中对应的不同通道所在的路径，实现匹配刚度
            img_file,  # 图片文件名
            model_id,  # 车型ID
            run_id  # 样本ID
        )

    def get_sample_info(self, img_file):
        """获取指定图片文件名的样本信息"""
        if img_file in self.samples_dict:
            return self.samples_dict[img_file]
        return None


# # # 使用示例和DataLoader测试
# if __name__ == "__main__":
#     # 设置数据集路径
#     dataset_path = r"E:\python_code\code\a.车门代码\2.预测模型搭建\model\all_sample_hdt_result\try\pipei\train"
#
#     # 创建数据集实例，dataset对象
#     dataset = CarDoorStiffnessDataset(
#         dataset_path,
#         target_size=(270, 270),  # 设置统一尺寸
#         padding_mode='constant'
#     )
#     print(f"数据集大小: {len(dataset)} 个样本")
#
#     # 测试DataLoader
#     from torch.utils.data import DataLoader
#
#     # 创建DataLoader
#     BATCH_SIZE = 4
#     train_dl = DataLoader(
#         dataset,
#         batch_size=BATCH_SIZE,
#         shuffle=True,
#         num_workers=0,  # 在Windows上建议设置为0，避免多进程问题
#         pin_memory=True if torch.cuda.is_available() else False
#     )
#
#     # 测试DataLoader
#     print("\n测试DataLoader:")
#     for i, batch in enumerate(train_dl):
#         images, stiffness, img_files, model_ids, run_ids = batch
#
#         print(f"批次 {i + 1}:")
#         print(f"  图像形状: {images.shape}")  # 应为 [B, C, H, W]
#         print(f"  刚度值形状: {stiffness.shape}")
#         print(f"  文件名: {img_files}")
#         print(f"  车型ID: {model_ids}")
#         print(f"  样本ID: {run_ids}")
#         # 只测试前两个批次
#         if i >= 1:
#             break

"""
samples_dict = {
    "R_006_00002_components_multi.png": {
        "cross_sections": "路径/cross_sections/R_006_00002_components_multi.png",
        "inner_points": "路径/inner_points/R_006_00002_components_multi.png",
        "stiffness": 161.6
    },
    "R_009_00011_components_multi.png": {
        # 类似结构...
    }
}
"""