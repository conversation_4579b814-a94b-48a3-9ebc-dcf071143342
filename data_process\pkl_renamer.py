import os
import re


def rename_pkl_files(directory):
    """
    批量重命名指定目录下的.pkl文件

    参数:
    directory (str): 要处理的目录路径
    """
    # 确保目录存在
    if not os.path.exists(directory):
        print(f"错误：目录 '{directory}' 不存在")
        return

    # 遍历目录中的所有文件
    for filename in os.listdir(directory):
        # 检查文件是否为.pkl文件且符合指定格式
        if filename.endswith('.pkl'):
            # 使用更灵活的正则表达式匹配文件名格式
            match = re.match(r'F-(.+?)_(\w+)\.pkl', filename)
            if match:
                # 获取原始文件名中的yyyyy部分
                yyyyy = match.group(2)

                # 构建新的文件名
                new_filename = f"calculation_{yyyyy}.pkl"

                # 构建完整的文件路径
                old_filepath = os.path.join(directory, filename)
                new_filepath = os.path.join(directory, new_filename)

                # 执行重命名
                try:
                    os.rename(old_filepath, new_filepath)
                    print(f"已重命名: {filename} -> {new_filename}")
                except Exception as e:
                    print(f"无法重命名 {filename}: {str(e)}")


if __name__ == "__main__":
    # 提示用户输入文件目录
    directory = input("请输入要处理的文件夹路径: ")

    # 执行重命名操作
    rename_pkl_files(directory)